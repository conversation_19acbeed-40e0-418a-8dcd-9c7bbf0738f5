#-*- coding: UTF-8 -*-
import openpyxl
import pymysql
import requests
from bs4 import BeautifulSoup
import lxml
import json
import time
import datetime
from dateutil.relativedelta import relativedelta

# wb=openpyxl.load_workbook(filename='市区.xlsx')
# ws=wb.active#注意！所有工作表的内容将合并到ws所代表的工作表中
# row_x=[]
# used=[]
# for sheet in wb: #遍历表
#     for row in sheet.iter_rows(min_row=2): #遍历表中各行，忽视表头故从第二行开始
#         temp_city = row[0].value
#         if temp_city not in used:
#             db = pymysql.connect(
#             host="127.0.0.1",
#             port=3336,
#             user="root",
#             password="FScatv@2016",
#             database="kkk")
#             cursor = db.cursor()
#             sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
#             cursor.execute(sql)
#             data = cursor.fetchone()
#             parentId=data[0]
#             startId = int(data[1])
#
#             endId = startId + 9
#             cursor.close()
#             db.close()
#             used.append(row[0].value)
#             for row in sheet.iter_rows(min_row=2):  # 遍历表中各行，忽视表头故从第二行开始
#                 if row[0].value ==temp_city:
#                     dd=[endId,startId,2,row[1].value,startId,1,parentId]
#                     startId=endId+1
#                     endId=startId+19
#                     print(dd)
#                     db = pymysql.connect(
#                         host="127.0.0.1",
#                         port=3336,
#                         user="root",
#                         password="FScatv@2016",
#                         database="kkk")
#                     cursor = db.cursor()
#                     sql = "insert into t_region(endRegionCode,regionCode,regionLevel,regionName,startRegionCode,state,parentRegionId) values(%s, %s, %s, %s, %s, %s, %s)"  # 注意是%s,不是s%
#                     cursor.execute(sql, dd)
#                     db.commit()
#                     cursor.close()
#                     db.close()
        # for row in sheet.iter_rows(min_row=2):  # 遍历表中各行，忽视表头故从第二行开始
        #     if row[0].value == temp_city:
        #         row_t = [endId, startId, 1, row[0].value, startId, 1, 76]
        #
        # print(data)
        # # for data in row_x:
        # #     cursor.execute(sql, data)
        # # db.commit()
        # cursor.close()
        # db.close()
        # row_t=[endId,startId,1,row[0].value,startId,1,76]
        # row_x.append(row_t)
        # startId=startId+1000
        # endId=endId+1000
        #取行中需要的单元格的内容组成列表并赋值给row_x
        #将row_x的值追加写入到工作表中
    #屏幕上显示点东西出来要不然怪无聊的
    #此处将显示正在处理的个工作表标题
# wb.save('yyyyyy.xlsx')
def zhixiasanji():
    wb=openpyxl.load_workbook(filename='2.xlsx')
    for sheet in wb: #遍历表
        for row in sheet.iter_rows(min_row=2): #遍历表中各行，忽视表头故从第二行开始
            city = ["天津市", "北京市", "重庆市", "上海市"]
            if row[0].value in city:
                db = pymysql.connect(
                host="127.0.0.1",
                port=3336,
                user="root",
                password="FScatv@2016",
                database="kkk")
                cursor = db.cursor()
                # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
                sql = f'select id from t_region where regionName = "{str(row[1].value)}"'  # 注意是%s,不是s%

                cursor.execute(sql)
                data = cursor.fetchone()
                regionId=str(data[0])

                sql = f'select id from t_address where location = "{str(row[0].value+row[0].value)}"'  # 注意是%s,不是s%

                cursor.execute(sql)
                data = cursor.fetchone()
                parentId = str(data[0])
                cursor.close()
                db.close()

                data = [row[0].value, row[0].value,row[1].value, row[0].value + row[0].value+row[1].value, regionId, 3, parentId]

                print(data)
                db = pymysql.connect(
                    host="127.0.0.1",
                    port=3336,
                    user="root",
                    password="FScatv@2016",
                    database="kkk")
                cursor = db.cursor()
                sql = "insert into t_address(province,city,district,location,regionId,level,parentId) values(%s, %s, %s, %s, %s, %s, %s)"  # 注意是%s,不是s%
                cursor.execute(sql, data)
                db.commit()
                cursor.close()
                db.close()

#增加直辖市二级
def zhixiaerji():
    wb=openpyxl.load_workbook(filename='2.xlsx')
    ws=wb.active#注意！所有工作表的内容将合并到ws所代表的工作表中
    row_x=[]
    used=[]
    city = ["天津市","北京市","重庆市","上海市"]
    for c in city:
        db = pymysql.connect(
        host="127.0.0.1",
        port=3336,
        user="root",
        password="FScatv@2016",
        database="kkk")
        cursor = db.cursor()
        # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
        sql = f'select id from t_region where regionName = "{c}"'  # 注意是%s,不是s%


        cursor.execute(sql)
        data = cursor.fetchone()
        regionId=data[0]

        sql = f'select id from t_address where location = "{c}"'  # 注意是%s,不是s%

        cursor.execute(sql)
        data = cursor.fetchone()
        parentId = data[0]
        cursor.close()
        db.close()
        data=[c,c,c+c,regionId,2,parentId]

        print(data)
        db = pymysql.connect(
            host="127.0.0.1",
            port=3336,
            user="root",
            password="FScatv@2016",
            database="kkk")
        cursor = db.cursor()
        sql = "insert into t_address(province,city,location,regionId,level,parentId) values(%s, %s, %s, %s, %s, %s)"  # 注意是%s,不是s%
        cursor.execute(sql, data)
        db.commit()
        cursor.close()
        db.close()

def yiji():
    wb=openpyxl.load_workbook(filename='1.xlsx')
    for sheet in wb: #遍历表
        for row in sheet.iter_rows(min_row=2): #遍历表中各行，忽视表头故从第二行开始
                db = pymysql.connect(
                host="127.0.0.1",
                port=3336,
                user="root",
                password="FScatv@2016",
                database="kkk")
                cursor = db.cursor()
                # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
                sql = f'select id from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%

                cursor.execute(sql)
                data = cursor.fetchone()
                regionId=data[0]
                cursor.close()
                db.close()
                data=[row[0].value,row[0].value,regionId,1,1660]

                print(data)
                db = pymysql.connect(
                    host="127.0.0.1",
                    port=3336,
                    user="root",
                    password="FScatv@2016",
                    database="kkk")
                cursor = db.cursor()
                sql = "insert into t_address(province,location,regionId,level,parentId) values(%s, %s, %s, %s, %s)"  # 注意是%s,不是s%
                cursor.execute(sql, data)
                db.commit()
                cursor.close()
                db.close()


#增加普通二级
def putongerji():
    wb=openpyxl.load_workbook(filename='2.xlsx')
    for sheet in wb: #遍历表
        for row in sheet.iter_rows(min_row=2): #遍历表中各行，忽视表头故从第二行开始
            city = ["天津市", "北京市", "重庆市", "上海市"]
            if not row[0].value in city:
                db = pymysql.connect(
                host="127.0.0.1",
                port=3336,
                user="root",
                password="FScatv@2016",
                database="kkk")
                cursor = db.cursor()
                # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
                sql = f'select id from t_region where regionName = "{str(row[1].value)}"'  # 注意是%s,不是s%

                cursor.execute(sql)
                data = cursor.fetchone()
                regionId=data[0]

                sql = f'select id from t_address where location = "{str(row[0].value)}"'  # 注意是%s,不是s%

                cursor.execute(sql)
                data = cursor.fetchone()
                parentId = str(data[0])
                cursor.close()
                db.close()
                data=[row[0].value,row[1].value,row[0].value+row[1].value,regionId,2,parentId]

                print(data)
                db = pymysql.connect(
                    host="127.0.0.1",
                    port=3336,
                    user="root",
                    password="FScatv@2016",
                    database="kkk")
                cursor = db.cursor()
                sql = "insert into t_address(province,city,location,regionId,level,parentId) values(%s, %s, %s, %s, %s, %s)"  # 注意是%s,不是s%
                cursor.execute(sql, data)
                db.commit()
                cursor.close()
                db.close()

def zhixiasiji():
    wb=openpyxl.load_workbook(filename='3.xlsx')
    for sheet in wb: #遍历表
        for row in sheet.iter_rows(min_row=2): #遍历表中各行，忽视表头故从第二行开始
            city = ["天津市", "北京市", "重庆市", "上海市"]
            if row[0].value in city:
                db = pymysql.connect(
                host="127.0.0.1",
                port=3336,
                user="root",
                password="FScatv@2016",
                database="kkk")
                cursor = db.cursor()
                # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
                sql = f'select id from t_region where regionName = "{str(row[1].value)}"'  # 注意是%s,不是s%
                print(row[1].value)
                cursor.execute(sql)
                data = cursor.fetchone()
                regionId=str(data[0])

                sql = f'select id from t_address where location = "{str(row[0].value+row[0].value+row[1].value)}"'  # 注意是%s,不是s%

                cursor.execute(sql)
                data = cursor.fetchone()
                parentId = str(data[0])
                cursor.close()
                db.close()

                data = [row[0].value, row[0].value,row[1].value,row[2].value, row[0].value + row[0].value+row[1].value+ row[2].value, regionId, 4, parentId]

                print(data)
                db = pymysql.connect(
                    host="127.0.0.1",
                    port=3336,
                    user="root",
                    password="FScatv@2016",
                    database="kkk")
                cursor = db.cursor()
                sql = "insert into t_address(province,city,district,towns,location,regionId,level,parentId) values(%s, %s, %s, %s, %s, %s, %s, %s)"  # 注意是%s,不是s%
                cursor.execute(sql, data)
                db.commit()
                cursor.close()
                db.close()



def putongsanji():
    wb=openpyxl.load_workbook(filename='3.5.xlsx')
    for sheet in wb: #遍历表
        for row in sheet.iter_rows(min_row=2): #遍历表中各行，忽视表头故从第二行开始
            city = ["天津市", "北京市", "重庆市", "上海市"]
            if not row[0].value in city:
                db = pymysql.connect(
                host="127.0.0.1",
                port=3336,
                user="root",
                password="FScatv@2016",
                database="kkk")
                cursor = db.cursor()
                # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
                sql = f'select id from t_region where regionName = "{str(row[1].value)}"'  # 注意是%s,不是s%

                cursor.execute(sql)
                data = cursor.fetchone()
                regionId=data[0]

                sql = f'select id from t_address where location = "{str(row[0].value+row[1].value)}"'  # 注意是%s,不是s%

                cursor.execute(sql)
                data = cursor.fetchone()
                parentId = str(data[0])
                cursor.close()
                db.close()
                data=[row[0].value,row[1].value,row[2].value,row[0].value+row[1].value+row[2].value,regionId,3,parentId]

                print(data)
                db = pymysql.connect(
                    host="127.0.0.1",
                    port=3336,
                    user="root",
                    password="FScatv@2016",
                    database="kkk")
                cursor = db.cursor()
                sql = "insert into t_address(province,city,district,location,regionId,level,parentId) values(%s, %s, %s, %s, %s, %s,%s)"  # 注意是%s,不是s%
                cursor.execute(sql, data)
                db.commit()
                cursor.close()
                db.close()




def putongsiji():
    wb=openpyxl.load_workbook(filename='3.xlsx')
    for sheet in wb: #遍历表
        for row in sheet.iter_rows(min_row=2): #遍历表中各行，忽视表头故从第二行开始
            city = ["天津市", "北京市", "重庆市", "上海市"]
            if not row[0].value in city:
                if not row[3].value == None and row[3].value != "":
                    db = pymysql.connect(
                    host="127.0.0.1",
                    port=3336,
                    user="root",
                    password="FScatv@2016",
                    database="kkk")
                    cursor = db.cursor()
                    # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
                    sql = f'select id from t_region where regionName = "{str(row[1].value)}"'  # 注意是%s,不是s%

                    cursor.execute(sql)
                    data = cursor.fetchone()
                    regionId=data[0]

                    sql = f'select id from t_address where location = "{str(row[0].value+row[1].value+row[2].value)}"'  # 注意是%s,不是s%

                    cursor.execute(sql)
                    data = cursor.fetchone()
                    parentId = str(data[0])
                    cursor.close()
                    db.close()
                    data=[row[0].value,row[1].value,row[2].value,row[3].value,row[0].value+row[1].value+row[2].value+row[3].value,regionId,4,parentId]

                    print(data)
                    db = pymysql.connect(
                        host="127.0.0.1",
                        port=3336,
                        user="root",
                        password="FScatv@2016",
                        database="kkk")
                    cursor = db.cursor()
                    sql = "insert into t_address(province,city,district,towns,location,regionId,level,parentId) values(%s, %s, %s, %s, %s, %s, %s, %s)"  # 注意是%s,不是s%
                    cursor.execute(sql, data)
                    db.commit()
                    cursor.close()
                    db.close()


# yiji()
# zhixiaerji()
# putongerji()
# zhixiasanji()
# putongsanji()
# zhixiasiji()
# putongsiji()





all_column = []
all_only_column=''
all_upshelf = []
all_asset = []
path = []

def get_parent(child_id):
    global path
    db = pymysql.connect(
        host="127.0.0.1",
        port=3336,
        user="root",
        password="FScatv@2016",
        database="newfaith")
    cursor = db.cursor()
    # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
    sql = f'SELECT parentId,columnName FROM t_column WHERE Id = {child_id}'  # 注意是%s,不是s%
    cursor.execute(sql)
    data = cursor.fetchone()
    cursor.close()
    db.close()
    if data[0] != 0:
        path.append(data[1])
        return get_parent(data[0])
    else:
        path.append(data[1])
        temppath = path
        path=[]
        return temppath



def get_column(parent_id):
    db = pymysql.connect(
        host="127.0.0.1",
        port=3336,
        user="root",
        password="FScatv@2016",
        database="newfaith")
    cursor = db.cursor()
    # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
    sql = f'SELECT id,hasChild,columnName,serviceId FROM t_column WHERE parentId = {parent_id} '  #and displayState=1 and serviceId is not null'  # 注意是%s,不是s%

    cursor.execute(sql)
    data = cursor.fetchall()


    for d in data:
        # print(d)
        return_id = d[0]

        sql = f'SELECT serviceName FROM t_services WHERE id ={d[3]}'  # 注意是%s,不是s%

        cursor.execute(sql)
        service_name = cursor.fetchone()
        service_name ='%s' % service_name

        all_column.append([return_id,service_name])
        if 1==d[1]:
            get_column(return_id)
        # print(return_id)

    sql = f'SELECT serviceName FROM t_services WHERE id = (select serviceId from t_column WHERE id={parent_id})'  # 注意是%s,不是s%

    cursor.execute(sql)
    service_name = cursor.fetchone()
    cursor.close()
    db.close()
    service_name = '%s' % service_name

    all_column.append([parent_id,service_name])
    return all_column




def get_upshelf(column_id):
    cls = ''
    count = 0
    for daa in column_id:
        cls += ','+str(daa[0])
    print(cls)
    for da in column_id:
        db = pymysql.connect(
            host="127.0.0.1",
            port=3336,
            user="root",
            password="FScatv@2016",
            database="newfaith")
        cursor = db.cursor()
        # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
        sql = f'SELECT assetId,upshelfTime FROM t_upshelf_column WHERE columnId = {da[0]}'  # 注意是%s,不是s%

        cursor.execute(sql)
        data = cursor.fetchall()
        cursor.close()
        db.close()

        for d in data:
            if d[0] != None:
                all_upshelf.append([d[0],da[1]])

        cursor = db.cursor()

        sql = f'SELECT articleId FROM t_upshelf_column WHERE columnId = {da[0]}'  # 注意是%s,不是s%
        db.ping(reconnect=True)
        cursor.execute(sql)
        data = cursor.fetchall()


        for d in data:
            if d[0] != None:
                # print(d[0])
                cursor = db.cursor()

                sql = f'SELECT subtitle FROM t_article WHERE Id = {d[0]}'  # 注意是%s,不是s%
                db.ping(reconnect=True)

                cursor.execute(sql)
                articles = cursor.fetchall()


                for a in articles:
                    article = '%s' % a[0]

                    if 'GDFS' in article:
                        print(article)
                        count+=1
                        print(count)
                        asid = article.split('=')[-1]

                        sql = f'SELECT id FROM t_asset WHERE assetId = \'{asid}\''  # 注意是%s,不是s%
                        db.ping(reconnect=True)
                        cursor.execute(sql)
                        aaa = cursor.fetchone()
                        cursor.close()
                        db.close()
                        print(aaa)
                        if aaa != None:
                            all_upshelf.append([aaa[0],da[1]])


    return all_upshelf


def get_upshelf_article(column_id):
    write = []
    assetids = []

    for da in column_id:
        db = pymysql.connect(
            host="127.0.0.1",
            port=3336,
            user="root",
            password="FScatv@2016",
            database="newfaith")
        cursor = db.cursor()
        # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
        sql = f'SELECT a.articleId,a.upshelfTime,b.columnName FROM t_upshelf_column a inner join t_column b on a.columnId=b.id WHERE a.columnId = {da[0]}'  # 注意是%s,不是s%

        cursor.execute(sql)
        data = cursor.fetchall()
        cursor.close()
        db.close()
        for d in data:
            if d[0] != None:
                cursor = db.cursor()
                sql = f'SELECT title,contentUrl FROM t_article WHERE Id = {d[0]} and status =1'  # 注意是%s,不是s%

                db.ping(reconnect=True)
                cursor.execute(sql)
                articles = cursor.fetchone()
                # for a in articles:

                try:
                    article = '%s' % articles[0]
                    assetId = "/"
                    if 'GDFS' not in article:
                        if ('' != articles[1]) &( articles[1] is not None):
                            assetId = '%s' % articles[1]
                            assetids.append(assetId.split('/')[1])

                        pp = get_parent(da[0])
                        pp.reverse()
                        pp = ('-'.join(pp))
                        print(pp)
                        print(article)
                        write.append([da[1],article,pp,d[1],assetId.split('/')[1]])
                except Exception as e:
                    print(e)
                    print(article)
                    print('error eeeeeeeeeeeeeeeeeeeee')
    print(assetids)
    get_asset(assetids)
    outwb = openpyxl.Workbook()  # 打开一个将写的文件
    outws = outwb.create_sheet(index=0)  # 在将写的文件创建sheet

    outws.cell(1, 1).value = '业务名'
    outws.cell(1, 2).value = '文章名'
    outws.cell(1, 3).value = '栏目名称'
    outws.cell(1, 4).value = '上架时间'
    outws.cell(1, 5).value = '媒资id'

    for row in range(0, len(write)):
        outws.cell(row + 2, 1).value = write[row][0]
        outws.cell(row + 2, 2).value = write[row][1]  # 写文件
        outws.cell(row + 2, 3).value = write[row][2]
        outws.cell(row + 2, 4).value = write[row][3]
        outws.cell(row + 2, 5).value = write[row][4]
        # print(row)
    saveExcel = "C:\\Users\\<USER>\\Desktop\\test.xlsx"
    outwb.save(saveExcel)  # 一定要记得保存


def get_upshelf_article_count(column_id):
    write = []
    assetids = []

    for da in column_id:
        db = pymysql.connect(
            host="127.0.0.1",
            port=3336,
            user="root",
            password="FScatv@2016",
            database="newfaith")
        cursor = db.cursor()
        # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
        sql = f'SELECT DISTINCT a.`articleId` FROM t_upshelf_column a WHERE a.`columnId` IN ({column_id})'  # 注意是%s,不是s%

        cursor.execute(sql)
        data = cursor.fetchall()
        cursor.close()
        db.close()
        print("栏目id："+column_id+"  图文数量："+data.count())




def get_asset(asset_id):
    outwb = openpyxl.Workbook()  # 打开一个将写的文件
    outws = outwb.create_sheet(index=0)  # 在将写的文件创建sheet
    for da in asset_id:
        try:
            db = pymysql.connect(
                host="127.0.0.1",
                port=3336,
                user="root",
                password="FScatv@2016",
                database="newfaith")
            cursor = db.cursor()
            # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
            sql = f'SELECT a.assetName,a.assetId,a.columnName,a.playNumber,a.runtime,a.synchronizeTime,c.columnName FROM t_asset a inner join t_upshelf_column b on a.id=b.assetId inner join t_column c on c.id=b.columnId  WHERE a.id = {da[0]}'  #媒资
            # sql = f'SELECT assetName,assetId,columnName,playNumber,runtime,synchronizeTime FROM t_asset WHERE assetId = "{da}"'  #文章 假媒资


            cursor.execute(sql)
            data = cursor.fetchall()
            cursor.close()
            db.close()

            for d in data:
                d=d+(da[1],)
                all_asset.append(d)
        except Exception as e:
            print(e)
            continue

    outws.cell(1, 1).value = '媒资名称'
    outws.cell(1, 2).value = '媒资id'
    outws.cell(1, 3).value = '上架媒资栏目'
    outws.cell(1, 4).value = '播放次数'
    outws.cell(1, 5).value = '媒资时长'
    outws.cell(1, 6).value = '同步时间'
    outws.cell(1, 7).value = '上架栏目'
    outws.cell(1, 8).value = '上架总栏目'



    for row in range(0, len(all_asset)):

        outws.cell(row + 2, 1).value = all_asset[row][0]  # 写文件
        outws.cell(row + 2, 2).value = all_asset[row][1]
        outws.cell(row + 2, 3).value = all_asset[row][2]
        outws.cell(row + 2, 4).value = all_asset[row][3]
        outws.cell(row + 2, 5).value = all_asset[row][4]
        outws.cell(row + 2, 6).value = all_asset[row][5]
        outws.cell(row + 2, 7).value = all_asset[row][6]
        outws.cell(row + 2, 8).value = all_asset[row][7]




        # print(row)
    saveExcel = "C:\\Users\\<USER>\\Desktop\\"+"123"+".xlsx"
    outwb.save(saveExcel)  # 一定要记得保存


def get_from_u1(columnId):
    url = 'http://**************/u1/GetFolderContents?client=****************&account=FS1378366&regionCode=1680&assetId='+columnId+'&profile=1&startAt=1&maxItems=100&resultType=json&clientip=*************'
    res = requests.get(url)
    data = json.loads(res.content)
    childNode = data['childNode']
    list = []
    for i in childNode:
        list.append(data['folderFrame']['displayName']+'--'+i['name'])
    print(list)

    outwb = openpyxl.Workbook()  # 打开一个将写的文件
    outws = outwb.create_sheet(index=0)  # 在将写的文件创建sheet
    for row in range(0, len(list)):
        outws.cell(row+1, 1).value = list[row]  # 写文件
        # print(row)
    saveExcel = "C:\\Users\\<USER>\\Desktop\\test.xlsx"
    outwb.save(saveExcel)  # 一定要记得保存
# get_from_u1('GDFS2420201010154515')


def send_sms():

    url = 'http://**************/sms/Api/Send.do'
    paras={
    'SpCode':'000001',
    'LoginName':'renzheng_gcable',
    'Password':'111111',
    'SerialNumber':'0000000'+str(int(time.time())),
    'MessageContent':'haha',
    'ScheduleTime':'',
    'UserNumber':['15622321650'],
    'f':''
    }
    res = requests.get(url,params=paras)
    print(res.text)


# get_upshelf(4015)
cha_id=5370
# cha_id=[7943,9225]
get_asset(get_upshelf(get_column(cha_id)))
# get_upshelf_article(get_column(cha_id))
# print(get_column(3855))
# print(all_column)
# for id in cha_id:
#     get_upshelf_article_count(get_only_column(id))


def get_bo_column():
    db = pymysql.connect(
        host="127.0.0.1",
        port=3336,
        user="root",
        password="FScatv@2016",
        database="newfaith")
    cursor = db.cursor()
    # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
    sql = f'SELECT DISTINCT columnId FROM t_bo_column WHERE columnPath like "%佛山新闻%";'  # 注意是%s,不是s%

    cursor.execute(sql)
    data = cursor.fetchall()
    cursor.close()
    db.close()

    for d in data:

        a7 = 0
        u1 = 0


        _id = d[0]

        url = "http://*************:8080/GetFolderContents"

        payload = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><GetFolderContents assetId = \""+_id+"\" account = \"FS11329687\" client = \"****************\" includeFolderProperties = \"Y\" includeSubFolder = \"Y\" includeSelectableItem = \"Y\" startAt = \"1\" />\r\n"
        headers = {
            'Content-Type': 'text/xml'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        # print(response.text)

        from xml.dom.minidom import parseString
        # minidom解析器打开xml文档并将其解析为内存中的一棵树
        DOMTree = parseString(str(response.text))
        # 获取xml文档对象，就是拿到树的根
        booklist = DOMTree.documentElement

        if booklist.hasAttribute('totalResults'):
            # 判断根节点booklist是否有type属性
            a7 = booklist.getAttribute('totalResults')
        #     print('根节点booklist的type属性值为：', booklist.getAttribute('totalResults'))
        # else:
        #     print('booklist 元素不存在type属性！！！')

        url = "http://**************/u1/GetFolderContents?client=****************&account=FS1378366&regionCode=1680&assetId="+_id+"&profile=1&maxItems=200&resultType=xml&clientip=*************&includeSubFolder=Y&includeFolderProperties=Y"

        payload = {}
        headers = {}

        response = requests.request("GET", url, headers=headers, data=payload)
        DOMTree = parseString(str(response.text))
        # 获取xml文档对象，就是拿到树的根
        booklist = DOMTree.documentElement

        if booklist.hasAttribute('totalResults'):
            # 判断根节点booklist是否有type属性
            u1 = booklist.getAttribute('totalResults')
        #     print('根节点booklist的type属性值为：', booklist.getAttribute('totalResults'))
        # else:
        #     print('booklist 元素不存在type属性！！！')

        if a7!=u1:
            print(_id)

def haha():
    for i in range(1,5):
        print(i)
        db = pymysql.connect(
            host="127.0.0.1",
            port=3336,
            user="root",
            password="FScatv@2016",
            database="faith_vote")
        cursor = db.cursor()
        # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
        sql = f'SELECT SUM(visitNum) FROM t_bi_visit_day WHERE serviceId = 62 and visitDate like "%2021-0'+str(i)+'%"'  # 注意是%s,不是s%

        cursor.execute(sql)
        data = cursor.fetchall()
        print(data)
        cursor.close()
        db.close()

# select * from t_visit where serviceCode = 'zhqx' and createTime like '%2020-02%' and fromAddr = 'portal';
# SELECT distinct smartCardId FROM t_visit WHERE serviceCode = 'zhqx' AND createTime LIKE '%2020-02%';




def get_longjiang():
    outwb = openpyxl.Workbook()  # 打开一个将写的文件
    outws = outwb.create_sheet(index=0)  # 在将写的文件创建sheet
    all_data = []

    db = pymysql.connect(
        host="127.0.0.1",
        port=3336,
        user="root",
        password="FScatv@2016",
        database="faith_vote")
    cursor = db.cursor()
    for i in range(0, 42):
        data_group = []
        y = datetime.datetime.strptime('2020-04-28', '%Y-%m-%d')
        x = y+ datetime.timedelta(days=7 * i)
        z = y+ datetime.timedelta(days=7 * (i+1)-1)
        print(str(x).split(" ")[0])
    # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
        sql = f'SELECT CAST(SUM(visitNum) as CHAR) FROM t_bi_visit_day WHERE DATE(visitDate) BETWEEN \'{str(x).split(" ")[0]}\' AND \'{str(z).split(" ")[0]}\' AND serviceId=62'

        cursor.execute(sql)
        data = cursor.fetchone()
        data_group.append(str(x).split(" ")[0]+'至'+str(z).split(" ")[0])

        data_group.append(data[0])
        print(data)
        all_data.append(data_group)

    cursor.close()
    db.close()

    print(data)

    for row in range(0, len(all_data)):
        outws.cell(row+1, 1).value = all_data[row][0]  # 写文件
        outws.cell(row + 1, 2).value = all_data[row][1]
        # print(row)
    saveExcel = "C:\\Users\\<USER>\\Desktop\\test.xlsx"
    outwb.save(saveExcel)  # 一定要记得保存

def get_longjiang_day():


    db = pymysql.connect(
        host="127.0.0.1",
        port=3336,
        user="root",
        password="FScatv@2016",
        database="faith_vote")
    cursor = db.cursor()
    for i in range(0, 11):
        y = datetime.datetime.strptime('2021-05-23', '%Y-%m-%d')
        x = y+ datetime.timedelta(days=i)
        print(str(x).split(" ")[0])
    # sql = f'select id,startRegionCode from t_region where regionName = "{str(row[0].value)}"'  # 注意是%s,不是s%
        sql = f'SELECT SUM(visitNum) FROM t_bi_visit_day WHERE DATE(visitDate) = \'{str(x).split(" ")[0]}\' AND serviceId=84'

        cursor.execute(sql)
        data = cursor.fetchall()
        print(data)
    cursor.close()
    db.close()

    print(data)


# get_longjiang()