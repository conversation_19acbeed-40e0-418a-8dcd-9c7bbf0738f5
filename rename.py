import os
import pymysql
import pandas as pd
import paramiko
import re
from sqlalchemy import create_engine
from urllib import parse

# 环境：python3.9

def print_hi(name):
    # 在下面的代码行中使用断点来调试脚本。
    print(f'Hi, {name}')  # 按 Ctrl+F8 切换断点。


# 视频文件重命名，mp4
# 子目录也一起改名
def vedio_rename(dir, df):
    # 打开需要把信息写入的文件
    txt = open("asset.txt", 'w', encoding='utf-8')
    for root, dirs, files in os.walk(dir):
        for file in files:
            if file.split(".")[1].lower() == "mp4" and df.index.values.tolist().__contains__(file.split(".")[0]):
                try:
                    txt.write(file.split(".")[0]+','+df.loc[file.split(".")[0], 'newname'] + "\n")

                    print("file:", root + "\\" + file, "改名")
                    print(df.loc[file.split(".")[0], 'newname'])
                    os.rename(root + "\\" + file,
                              root + "\\" + df.loc[file.split(".")[0], 'newname'] + '.' + file.split(".")[1].lower())
                except Exception as e:
                    print(file+"改名失败")
    txt.close()

def vedio_upload(username,password,hostname,port,dir):
    count = 0
    global t
    # ssh连接
    t = paramiko.Transport((hostname, port))
    t.connect(username=username, password=password)
    sftp = paramiko.SFTPClient.from_transport(t)
    # sftp上传
    for root, dirs, files in os.walk(dir):
        for file in files:
            #数据库里有匹配的才上传,规则：改名后全英文文件名
            CN_PATTERN = re.compile(u'[\u4e00-\u9fa5]')
            cn = CN_PATTERN.findall(file)
            if len(cn) == 0 and file.split(".")[1].lower() == "mp4":
                sftp.put(root + "\\" + file, "/data1/resource/jyj_resource/" + file)
                print(file + ' 上传完成')
                count += 1
    print('总上传：'+str(count))

def check_asset_id():
    # 读取asset.txt文件存到dict里
    txt = open("asset.txt", 'r', encoding='utf-8')
    dict = {}
    for line in txt.readlines():
        asset = line.split(',')[0].split('-')[1]+line.split(',')[0].split('-')[2]+'-'+line.split(',')[0].split('-')[3]
        # print(asset)
        dict[asset] = line.split(',')[1].replace('\n', '')
    txt.close()
    # 读取数据库里的数据
    engine = create_engine('mysql+pymysql://%s:%s@%s:%s/%s?charset=utf8'
                           % ('vote', parse.quote_plus('Faith@2016'), '127.0.0.1', '3326', 'gcable_vote'))

    sql = 'select assetName,assetCode from t_asset'
    df = pd.read_sql(sql, engine, index_col="assetName")
    # 比较,记录不同的数量和相同的数量
    same = 0
    not_match = 0
    for index, row in df.iterrows():
        # print(index.__str__())
        # index_name = index.__str__()
        # print(row['assetCode'])
        # print(dict.get(index))
        # print(dict[index_name])
        #比较数据库里的assetCode和dict里的assetCode

        if dict.get(index) != None:
            if row['assetCode'] != dict.get(index.__str__()):
                print(index + ' 数据库：' + row['assetCode'] + ' 本地：' + dict[index])
                not_match += 1
            else:
            # print(index + ' 本地没有')
                same += 1
    print('相同：' + str(same) + ' 不同：' + str(not_match))


# 按间距中的绿色按钮以运行脚本。
if __name__ == '__main__':
    # 配置开始，配置后运行
    # 视频文件所在路径
    dir = 'F:\\tmp'
    # 数据库数据查询，name是文件名，根据名称查找，需要指定新名字的列为newname
    sql = 'select assetName as name,previewAssetId as newname from t_source_asset'
    # db_conn = pymysql.connect(
    #     host='**************',
    #     port=3306,
    #     user='faith',
    #     password='Faith@2016',
    #     database='newfaith',
    #     charset='utf8'
    # )
    engine = create_engine('mysql+pymysql://%s:%s@%s:%s/%s?charset=utf8'
                           % ('faith', parse.quote_plus('Faith@2016'), '127.0.0.1', '3326', 'newfaith'))
    # ssh配置
    username = "fscatv"
    password = "4#4TuuPi&2"
    hostname = "*************"
    port = 22
    # 配置结束

    # 读取数据到df
    # df = pd.read_sql(sql, engine, index_col="name")

    # vedio_rename(dir, df)
    # vedio_upload(username,password,hostname,port,dir)
    # t.close()
    # check_asset_id()