# -*- coding: UTF-8 -*-
import datetime
import random
import xlrd2 as xlrd
import pymysql

db = pymysql.connect(
    host="127.0.0.1",
    port=13306,
    user="superman",
    password="FScatv@2016",
    database="gcable_shop")
cursor = db.cursor()


def generate_sp(product_namec):
    state = 1
    allowReturn = 7
    auditState = 1
    commodityClassificationCode = 1
    isCourier = 1
    isDelivery = 1
    isGlobal = 0
    isSelfSupport = 1
    isTop = 1
    orderFlowType = 0
    pickupSupport = 0
    productName = product_namec
    taxRate = 0
    brandId = 1
    storeId = 2
    productType = 0
    level1Id = 309
    lastModifyTime = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')
    auditTime = lastModifyTime

    data = [
        state, allowReturn, auditState, commodityClassificationCode, isCourier, isDelivery, isGlobal, isSelfSupport,
        isTop, orderFlowType, pickupSupport, productName, taxRate, brandId, storeId, productType, level1Id,
        lastModifyTime, auditTime
    ]

    return data


def generate_spdetail(mobileProductInfo, specialTemplatec, specificationId):
    state = 1
    mobileProductInfo = mobileProductInfo
    productInfo = mobileProductInfo
    specialTemplate = "[{\"name\":\"规格型号\",\"options\":[\"" + specialTemplatec + "\"]}]"
    specificationId = specificationId
    specifications = "[{\"group\":\"主体参数\",\"parameters\":[{\"name\":\"规格型号\",\"isSearch\":0,\"global\":0,\"options\":[\"" + specialTemplatec + "\"],\"isNumber\":0,\"unitName\":\"\",\"value\":\"" + specialTemplatec + "\"}]}]"
    lastModifyTime = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')
    data = [
        state, mobileProductInfo, productInfo, specialTemplate, specificationId, specifications, lastModifyTime
    ]

    return data


def generate_product(specialTemplate, priceUnit, productPrice, specificationId):
    now_time = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    first = random.choice(range(1, 10))
    leftover = set(range(10)) - {first}
    rest = random.sample(leftover, 3)
    digits = [first] + rest

    state = 1
    beginSale = 1
    isMarketable = 1
    isPromotion = 0
    priceUnit = priceUnit
    productCode = 'PROD' + now_time + str(digits[0]) + str(digits[1]) + str(digits[2]) + str(digits[3])
    productPrice = productPrice
    skuSpecification = "{\"规格型号\":\"" + specialTemplate + "\"}"
    specificationIndex = 0
    productImageId = 98490
    productPhoneImageId = 98490
    specificationId = specificationId
    pickupSupport = 0
    salesNumber = 0
    inventoryAmount = 1000
    subtractType = 0
    lastModifyTime = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')

    data = [
        state, beginSale, isMarketable, isPromotion, priceUnit, productCode, productPrice, skuSpecification,
        specificationIndex, productImageId, productPhoneImageId, specificationId, pickupSupport, inventoryAmount,
        subtractType, lastModifyTime, salesNumber
    ]

    return data


def generate_product_price(priceUnit, productPrice, productid):
    first = random.choice(range(1, 10))
    leftover = set(range(10)) - {first}
    rest = random.sample(leftover, 3)
    digits = [first] + rest

    state = 1
    disableTime = datetime.datetime.strftime(datetime.datetime.now() + datetime.timedelta(weeks=260),
                                             '%Y-%m-%d %H:%M:%S')
    enableTime = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')
    lastModifyTime = enableTime
    priceType = 0
    priceUnit = priceUnit
    salesPrice = productPrice
    productId = productid
    isDefault = 1

    data = [
        state, disableTime, enableTime, priceType, priceUnit, salesPrice, productId, isDefault, lastModifyTime
    ]

    return data


def generate_product_limit(specificationId, productid):
    state = 1
    storeId = 2
    specificationId = specificationId
    productId = productid
    limitScope = 0
    limitType = 0
    inventoryAmount = 1000
    limitState = 1
    endTime = datetime.datetime.strftime(datetime.datetime.now() + datetime.timedelta(weeks=52),
                                         '%Y-%m-%d %H:%M:%S')
    startTime = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')
    lastModifyTime = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')
    data = [
        state, storeId, specificationId, productId, limitScope, limitType, inventoryAmount, limitState, endTime,
        startTime, lastModifyTime
    ]

    return data


# 导入需要读取的第一个Excel表格的路径

data1 = xlrd.open_workbook(r'ssfl.xlsx')

table = data1.sheets()[0]


def import_excel(excel):
    for rown in range(excel.nrows):
        # for rown in range(1):

        print(table.cell_value(rown + 2, 0))
        print(table.cell_value(rown + 2, 1))
        print(table.cell_value(rown + 2, 2))
        print(table.cell_value(rown + 2, 3))
        print(table.cell_value(rown + 2, 4))

        # 写入sp表
        sql = "insert into t_product_specification(state,allowReturn,auditState,commodityClassificationCode,isCourier,isDelivery,isGlobal,isSelfSupport,isTop,orderFlowType,pickupSupport,productName,taxRate,brandId,storeId,productType,level1Id,lastModifyTime,auditTime) values(%s, %s, %s, %s, %s, %s,%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"  # 注意是%s,不是s%

        cursor.execute(sql, generate_sp(table.cell_value(rown + 2, 0)))
        spid = db.insert_id()
        db.commit()

        # 写入地区表
        sql = "insert into t_specification_visible_region(specificationId,regionId) values(%s, %s)"
        cursor.execute(sql, (spid, 77))
        db.commit()

        sql = "insert into t_product_specification_region(specificationId,regionId) values(%s, %s)"
        cursor.execute(sql, (spid, 77))
        db.commit()

        # 写入sp detail表
        sql = "insert into t_product_specification_detail(state,mobileProductInfo,productInfo,specialTemplate,specificationId,specifications,lastModifyTime) values(%s, %s, %s, %s, %s, %s, %s)"  # 注意是%s,不是s%

        cursor.execute(sql, generate_spdetail(table.cell_value(rown + 2, 2), table.cell_value(rown + 2, 1), spid))
        spdetailid = db.insert_id()
        db.commit()

        # sp detail id 回填sp表
        sql = "update t_product_specification set detailId = %s where id = %s"  # 注意是%s,不是s%
        cursor.execute(sql, (spdetailid, spid))
        db.commit()

        # 写入product表
        sql = "insert into t_product(state,beginSale,isMarketable,isPromotion,priceUnit,productCode,productPrice,skuSpecification,specificationIndex,productImageId,productPhoneImageId,specificationId,pickupSupport,inventoryAmount,subtractType,lastModifyTime,salesNumber) values(%s, %s, %s, %s, %s, %s,%s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s)"  # 注意是%s,不是s%

        cursor.execute(sql, generate_product(table.cell_value(rown + 2, 1), table.cell_value(rown + 2, 3),
                                             table.cell_value(rown + 2, 4), spid))
        productid = db.insert_id()
        db.commit()

        # 写入price表
        sql = "insert into  t_product_price(state,disableTime,enableTime,priceType,priceUnit,salesPrice,productId,isDefault,lastModifyTime) values(%s, %s, %s, %s, %s, %s,%s, %s, %s)"  # 注意是%s,不是s%

        cursor.execute(sql, generate_product_price(table.cell_value(rown + 2, 3),
                                                   table.cell_value(rown + 2, 4), productid))
        db.commit()

        # 写入limit表
        sql = "insert into  t_product_limit(state,storeId,specificationId,productId,limitScope,limitType,inventoryAmount,limitState,endTime,startTime,lastModifyTime) values(%s, %s, %s, %s, %s, %s,%s, %s,%s, %s, %s)"  # 注意是%s,不是s%

        cursor.execute(sql, generate_product_limit(spid, productid))
        db.commit()


def update_excel(excel):
    for rown in range(excel.nrows-1):
    # for rown in range(1):

        id = "%d" % table.cell_value(rown + 1, 0)
        flbm ="%d" % table.cell_value(rown + 1, 1)
        print(id)
        print(flbm)
        print(table.cell_value(rown + 1, 2))



        # 写入sp表
        sql = "UPDATE t_product_specification set commodityClassificationCode = "+ flbm +" where id = "+ id  # 注意是%s,不是s%

        cursor.execute(sql)
        db.commit()





update_excel(table)

cursor.close()
db.close()

