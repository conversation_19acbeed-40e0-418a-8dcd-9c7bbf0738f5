# *_* coding :UTF-8 *_*
import os

import openpyxl
from aip import AipOcr
import requests
import chardet
import sys
import math

x_pi = 3.14159265358979324 * 3000.0 / 180.0
pi = 3.1415926535897932384626  # π
a = 6378245.0  # 长半轴
ee = 0.00669342162296594323  # 偏心率平方

def bd09_to_gcj02(bd):
    bd_lon = float(bd.split(',')[0])
    bd_lat = float(bd.split(',')[1])
    x = bd_lon - 0.0065
    y = bd_lat - 0.006
    z = math.sqrt(x * x + y * y) - 0.00002 * math.sin(y * x_pi)
    theta = math.atan2(y, x) - 0.000003 * math.cos(x * x_pi)
    gg_lng = z * math.cos(theta)
    gg_lat = z * math.sin(theta)
    # return [gg_lng, gg_lat]
    return str(gg_lng)+','+str(gg_lat)

# 执行一次高德地图地理逆编码的查询
# 输入值：coordList -> 经纬度的序列,currentKey -> 当前使用的Key
# 返回值：resultList -> 查询成功，返回结果地址的序列
#        -1 -> 执行当前查询时Key的配额用完了
#        -2 -> 执行当前查询出错
def ExcuteSingleQuery(coordString ):
    # 1-将coordList中的经纬度坐标连接成高德地图API能够识别的样子
    # coordString = ""     # 当前coordList组成的string
    # for currentCoord in coordList:
    #     coordString += str(currentCoord[0]) + "," + str(currentCoord[1]) + '|'
    # 2-地理编码查询需要的Url
    output = 'json'    # 查询返回的形式
    batch = 'false'     # 是否支持多个查询
    base = 'https://restapi.amap.com/v3/geocode/regeo?'    # 逆地理编码查询Url的头
    currentUrl = base + "output=" + output + "&batch=" + batch + "&location=" + coordString+ "&key=ca6784cc3d540eb3661efd97d25539fa"
    # 3-提交请求
    response = requests.get(currentUrl)    # 提交请求
    answer = response.json()   # 接收返回
    # 4-解析Json的内容
    resultList = []    # 用来存放逆地理编码结果的空序列
    if answer['status'] == '1' and answer['info'] == 'OK':
        # 4.1-请求和返回都成功，则进行解析
        tmpList = answer['regeocode']    # 获取所有结果坐标点

        try:
            # 解析','分隔的经纬度
            addressString = tmpList['formatted_address']
            # 放入结果序列
            resultList.append(addressString)
        except:
            # 如果发生错误则存入None
            resultList.append(None)
        return addressString
    elif answer['info'] == 'DAILY_QUERY_OVER_LIMIT':
        # 4.2-当前账号的余额用完了,返回-1
        return -1
    else:
        # 4.3-如果发生其他错误则返回-2
        return -2




# 定义常量, 自己申请的应用信息
APP_ID = '27980749'  # AppID
API_KEY = 'u04jKXkkTeUw2FtNbp9swoTc'  # API Key
SECRET_KEY = 'jrzMuAqR3t4WtSyq7iK62ZyDdE1GisZG'  # Secret Key

# ExcuteSingleQuery(bd09_to_gcj02(113.063705,23.014264))

client = AipOcr(APP_ID, API_KEY, SECRET_KEY)  # 初始化AipFace对象

# path = "F:/tmp/pics"  # 读取图片
path = os.path.dirname(os.path.abspath(sys.argv[0]))  # 读取图片
path = path.replace('\\', '/')
all_picture_path = os.listdir(path)  # 统计文件下图片个数

# file = open("F:/tmp/orc_info.txt", 'w', encoding='utf-8')  # 打开你一会需要把信息写入的文件
file = open("orc_info.txt", 'w', encoding='utf-8')  # 打开你一会需要把信息写入的文件


def get_file_content(picture_path):
    with open(picture_path, 'rb') as fp:
        return fp.read()


options = {"language_type": "CHN_ENG", "detect_direction": "true", "detect_language": "true", "probability": "true"}
returnRes = []

# 迭代识别文件下的所有图片
for path in all_picture_path:

    if 'jpg' in path or 'JPG' in path or 'jpeg' in path or 'JPEG' in path or 'png' in path or 'PNG' in path:
        # picture_path = os.path.join('F:', 'tmp', 'pics', path)
        picture_path = os.path.join(path)


        result = client.basicGeneral(get_file_content(picture_path))  # 接收aip返回的识别结果
        reg_res = "识别结果:\r\n"
        print(result)
        neddedRe = ['网络运营商', '数据网', '小区类型', 'NR-PCI', 'NR-CI', 'PCI', 'CI', 'ECI']
        tempResult = {}
        if 'words_result' in result:
            for i in range(len(result['words_result'])):
                # for seg in result['words_result']:
                # print(result['words_result'][i]['words'])
                if '信号强度' in result['words_result'][i]['words']:
                    if result['words_result'][i + 5]['words'] == 'SS':
                        tempResult['信号强度'] = result['words_result'][i + 1]['words'] + '  ' + \
                                             result['words_result'][i + 2]['words'] + '  ' + \
                                             result['words_result'][i + 3]['words'] + '  ' + \
                                             result['words_result'][i + 4]['words'] + '  ' + \
                                             result['words_result'][i + 5]['words'] + '  ' + \
                                             result['words_result'][i + 6]['words'] + '  ' + \
                                             result['words_result'][i + 7]['words'] + ':' + \
                                             result['words_result'][i + 8]['words'] + '  ' + \
                                             result['words_result'][i + 9]['words'] + '  ' + \
                                             result['words_result'][i + 10]['words'] + '  ' + \
                                             result['words_result'][i + 11]['words'] + '  ' + \
                                             result['words_result'][i + 12]['words'] + '  ' + \
                                             result['words_result'][i + 13]['words'] + '  ' + \
                                             result['words_result'][i + 14][
                                                 'words'] + ':' + '                   ' + (
                                                 result['words_result'][i + 15]['words'] if
                                                 result['words_result'][i + 15]['words'] != '-80' else '——') + '  ' + (
                                                 result['words_result'][i + 16]['words'] if
                                                 result['words_result'][i + 15]['words'] != '-80' else '——') + '  ' + (
                                                 result['words_result'][i + 17]['words'] if
                                                 result['words_result'][i + 15]['words'] != '-80' else '——') + '  '
                    else:
                        tempResult['信号强度'] = result['words_result'][i + 1]['words'] + '  ' + \
                                             result['words_result'][i + 2]['words'] + '  ' + \
                                             result['words_result'][i + 3]['words'] + '  ' + \
                                             result['words_result'][i + 4]['words'] + ':' + \
                                             result['words_result'][i + 5]['words'] + '  ' + \
                                             result['words_result'][i + 6]['words'] + '  ' + \
                                             result['words_result'][i + 7]['words'] + '  ' + \
                                             result['words_result'][i + 8]['words'] + '  '

                if '我的位置' in result['words_result'][i]['words']:
                    tempResult['我的位置'] = ExcuteSingleQuery(bd09_to_gcj02(result['words_result'][i]['words'].replace('：','').replace(',','').replace('我的位置','').replace('/', ',')))
                    tempResult['经度'] = result['words_result'][i]['words'].replace('：','').replace(',','').replace('我的位置','').split('/')[0]
                    tempResult['纬度'] = result['words_result'][i]['words'].replace('：','').replace(',','').replace('我的位置','').split('/')[1]


                if result['words_result'][i]['words'] in neddedRe:
                    if result['words_result'][i]['words'] not in tempResult.keys():
                        tempResult[result['words_result'][i]['words']] = result['words_result'][i + 1]['words']

                if 'FREQ' in result['words_result'][i]['words']:
                    if result['words_result'][i]['words'] not in tempResult.keys():
                        tempResult['FREQ'] = result['words_result'][i + 1]['words']
                tempResult['图片名'] = picture_path
                reg_res += result['words_result'][i]['words'] + "\r\n"
            file.write(picture_path + "\r\n")
            # file.write(reg_res + "\r\n")

            file.write('#########################################' + "\r\n")
            for k, v in tempResult.items():
                if k == '信号强度':
                    file.write(k + "\r\n")
                    for i in v.split(':'):
                        file.write(i + "\r\n")
                else:
                    file.write(k + "\r\n")
                    file.write(v + "\r\n")
                file.write("\r\n")
            file.write('#########################################' + "\r\n" + "\r\n")

            # print('##########################################')
            # print(tempResult)
            # print('##########################################')

        if tempResult.get('NR-PCI') is not None:
            tempResult['PCI'] = tempResult.get('NR-PCI')
        else:
            tempResult['PCI'] = tempResult.get('PCI')

        if tempResult.get('ECI') is not None:
            tempResult['ECI'] = tempResult.get('ECI').split('(')[-1].replace(')', '')
        else:
            print(tempResult.get('NR-CI'))
            tempResult['ECI'] = tempResult.get('NR-CI').split('(')[-1].replace(')', '')


        returnRes.append(tempResult)


outwb = openpyxl.Workbook()  # 打开一个将写的文件
outws = outwb.create_sheet(index=0)  # 在将写的文件创建sheet

outws.cell(1, 1).value = '图片名'
outws.cell(1, 2).value = '运营商'
outws.cell(1, 3).value = '数据网'
outws.cell(1, 4).value = '经度'
outws.cell(1, 5).value = '纬度'
outws.cell(1, 6).value = '位置'
outws.cell(1, 7).value = 'PCI'
outws.cell(1, 8).value = 'ECI'
outws.cell(1, 9).value = 'FREQ'
outws.cell(1, 10).value = '信号强度'


for row in range(0, len(returnRes)):
    outws.cell(row + 2, 1).value = returnRes[row].get('图片名')
    outws.cell(row + 2, 2).value = returnRes[row].get('网络运营商')
    outws.cell(row + 2, 3).value = returnRes[row].get('数据网')
    outws.cell(row + 2, 4).value = returnRes[row].get('经度')
    outws.cell(row + 2, 5).value = returnRes[row].get('纬度')
    outws.cell(row + 2, 6).value = returnRes[row].get('我的位置')
    outws.cell(row + 2, 7).value = returnRes[row].get('PCI')
    outws.cell(row + 2, 8).value = returnRes[row].get('ECI')
    outws.cell(row + 2, 9).value = returnRes[row].get('FREQ')
    outws.cell(row + 2, 10).value = returnRes[row].get('信号强度').replace(':','\r\n')
    # print(row)
# saveExcel = "C:\\Users\\<USER>\\Desktop\\test.xlsx"
saveExcel = "ocr.xlsx"

outwb.save(saveExcel)  # 一定要记得保存

file.close()

