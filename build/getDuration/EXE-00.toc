('C:\\Users\\<USER>\\Desktop\\monthly_report\\dist\\getDuration.exe',
 True,
 False,
 False,
 'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 <PERSON>als<PERSON>,
 False,
 '<?xml version="1.0" encoding="UTF-8" standalone="yes"?><assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"><assemblyIdentity type="win32" name="getDuration" processorArchitecture="amd64" version="1.0.0.0"/><trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo><dependency><dependentAssembly><assemblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" language="*" processorArchitecture="*" version="6.0.0.0" publicKeyToken="6595b64144ccf1df"/></dependentAssembly></dependency><compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1"><application><supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/><supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/><supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/><supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/><supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/></application></compatibility><application xmlns="urn:schemas-microsoft-com:asm.v3"><windowsSettings><longPathAware xmlns="http://schemas.microsoft.com/SMI/2016/WindowsSettings">true</longPathAware></windowsSettings></application></assembly>',
 True,
 True,
 False,
 None,
 None,
 None,
 'C:\\Users\\<USER>\\Desktop\\monthly_report\\build\\getDuration\\getDuration.pkg',
 [('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\build\\getDuration\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\build\\getDuration\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\build\\getDuration\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\build\\getDuration\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\build\\getDuration\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_subprocess',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_subprocess.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('getDuration',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\getDuration.py',
   'PYSOURCE'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('python38.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\python38.dll',
   'BINARY'),
  ('libopenblas.FB5AE2TYXYH2IJRDKGDGQ3XBKLKTF43H.gfortran-win_amd64.dll',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\.libs\\libopenblas.FB5AE2TYXYH2IJRDKGDGQ3XBKLKTF43H.gfortran-win_amd64.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg490_64.dll',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg490_64.dll',
   'BINARY'),
  ('_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('select',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('pyexpat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('unicodedata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_elementtree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml.etree',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\etree.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml._elementpath',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\_elementpath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.sax',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\sax.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.objectify',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\objectify.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.html.diff',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\html\\diff.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.html.clean',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\html\\clean.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('lxml.builder',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\builder.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_tests',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_umath',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.linalg.lapack_lite',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.mtrand',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._sfc64',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._philox',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\random\\_philox.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._pcg64',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._mt19937',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.bit_generator',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._generator',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\random\\_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._bounded_integers',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._common',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\random\\_common.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.fft._pocketfft_internal',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.linalg._umath_linalg',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('cv2.cv2',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\python3.dll',
   'BINARY'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\build\\getDuration\\base_library.zip',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('cv2\\misc\\version.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\config-3.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('cv2\\load_config_py2.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\load_config_py2.py',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('cv2\\config.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('cv2\\version.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA')],
 [],
 False,
 False,
 1711096828,
 [('run.exe',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\venv\\lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit\\run.exe',
   'EXECUTABLE')])
