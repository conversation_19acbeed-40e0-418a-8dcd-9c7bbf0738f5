(['C:\\Users\\<USER>\\Desktop\\monthly_report\\ocr.py'],
 ['C:\\Users\\<USER>\\Desktop\\monthly_report'],
 ['codecs'],
 ['c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks\\__pycache__',
  'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks',
  'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\__pycache__',
  'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks',
  'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\numpy\\_pyinstaller'],
 {},
 [],
 [],
 False,
 False,
 False,
 {},
 '3.8.10 (tags/v3.8.10:3d8993a, May  3 2021, 11:48:03) [MSC v.1928 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_subprocess.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('ocr',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\ocr.py',
   'PYSOURCE')],
 [('multiprocessing.popen_spawn_posix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('threading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('contextlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('_strptime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('datetime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\datetime.py',
   'PYMODULE'),
  ('calendar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('copy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\gettext.py',
   'PYMODULE'),
  ('struct',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\struct.py',
   'PYMODULE'),
  ('shutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\shutil.py',
   'PYMODULE'),
  ('zipfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('configparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\configparser.py',
   'PYMODULE'),
  ('email',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('socket',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\selectors.py',
   'PYMODULE'),
  ('random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\random.py',
   'PYMODULE'),
  ('hashlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pprint.py',
   'PYMODULE'),
  ('_compat_pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\string.py',
   'PYMODULE'),
  ('bisect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bisect.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\getopt.py',
   'PYMODULE'),
  ('quopri',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\quopri.py',
   'PYMODULE'),
  ('uu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\optparse.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\csv.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bz2.py',
   'PYMODULE'),
  ('signal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\signal.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('ssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ssl.py',
   'PYMODULE'),
  ('http',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\numbers.py',
   'PYMODULE'),
  ('hmac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\hmac.py',
   'PYMODULE'),
  ('tempfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tempfile.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\runpy.py',
   'PYMODULE'),
  ('pkgutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\zipimport.py',
   'PYMODULE'),
  ('inspect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\inspect.py',
   'PYMODULE'),
  ('ast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ast.py',
   'PYMODULE'),
  ('dis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\opcode.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('getpass',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('_py_abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\typing.py',
   'PYMODULE'),
  ('tracemalloc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('chardet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.enums',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('requests',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('requests.models',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('requests.hooks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('dummy_threading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('requests.auth',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('__future__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\__future__.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('ipaddress',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ipaddress.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.request',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3._collections',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.response',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.packages',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x509',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x509.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x448',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs7.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('bcrypt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bcrypt.__about__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\bcrypt\\__about__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x25519',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.poly1305',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\poly1305.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hmac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hashes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed448',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed25519',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dsa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.scrypt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\scrypt.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('aip',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\__init__.py',
   'PYMODULE'),
  ('aip.easydl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\easydl.py',
   'PYMODULE'),
  ('aip.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\base.py',
   'PYMODULE'),
  ('aip.imageprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\imageprocess.py',
   'PYMODULE'),
  ('aip.bodyanalysis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\bodyanalysis.py',
   'PYMODULE'),
  ('aip.imagesearch',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\imagesearch.py',
   'PYMODULE'),
  ('aip.imageclassify',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\imageclassify.py',
   'PYMODULE'),
  ('aip.speech',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\speech.py',
   'PYMODULE'),
  ('aip.kg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\kg.py',
   'PYMODULE'),
  ('aip.imagecensor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\imagecensor.py',
   'PYMODULE'),
  ('aip.face',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\face.py',
   'PYMODULE'),
  ('aip.nlp',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\nlp.py',
   'PYMODULE'),
  ('aip.ocr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\aip\\ocr.py',
   'PYMODULE'),
  ('openpyxl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('html.entities',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('webbrowser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\webbrowser.py',
   'PYMODULE'),
  ('glob',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\glob.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('cgi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\cgi.py',
   'PYMODULE'),
  ('doctest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\doctest.py',
   'PYMODULE'),
  ('unittest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.async_case',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.result',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pdb.py',
   'PYMODULE'),
  ('pydoc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pydoc.py',
   'PYMODULE'),
  ('http.server',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\socketserver.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tty.py',
   'PYMODULE'),
  ('sysconfig',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\sysconfig.py',
   'PYMODULE'),
  ('platform',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\platform.py',
   'PYMODULE'),
  ('code',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\codeop.py',
   'PYMODULE'),
  ('bdb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\cmd.py',
   'PYMODULE'),
  ('difflib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\difflib.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.compat._pep440',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\compat\\_pep440.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('dataclasses',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE')],
 [('VCRUNTIME140.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('python38.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\python38.dll',
   'BINARY'),
  ('libopenblas.FB5AE2TYXYH2IJRDKGDGQ3XBKLKTF43H.gfortran-win_amd64.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\.libs\\libopenblas.FB5AE2TYXYH2IJRDKGDGQ3XBKLKTF43H.gfortran-win_amd64.dll',
   'BINARY'),
  ('_multiprocessing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('select',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_hashlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('pyexpat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('unicodedata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_cffi_backend',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography.hazmat.bindings._openssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_openssl.pyd',
   'EXTENSION'),
  ('cryptography.hazmat.bindings._rust',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('bcrypt._bcrypt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('_elementtree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml.etree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\etree.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml._elementpath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\_elementpath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.sax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\sax.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.objectify',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\objectify.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.html.diff',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\diff.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.html.clean',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\clean.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('lxml.builder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\builder.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_tests',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_umath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.linalg.lapack_lite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.mtrand',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._sfc64',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._philox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_philox.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._pcg64',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._mt19937',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.bit_generator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._generator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._bounded_integers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_common.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.fft._pocketfft_internal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.linalg._umath_linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('libcrypto-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\python3.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\build\\ocr\\base_library.zip',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\LICENSE.APACHE',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\RECORD',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\RECORD',
   'DATA'),
  ('certifi\\py.typed',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\LICENSE',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\LICENSE',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\METADATA',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\METADATA',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\LICENSE.BSD',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\WHEEL',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\LICENSE.PSF',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\LICENSE.PSF',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA')],
 [])
