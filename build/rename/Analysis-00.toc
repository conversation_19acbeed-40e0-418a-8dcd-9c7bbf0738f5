(['C:\\Users\\<USER>\\Desktop\\monthly_report\\rename.py'],
 ['C:\\Users\\<USER>\\Desktop\\monthly_report'],
 ['codecs'],
 ['c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks\\__pycache__',
  'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks',
  'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\__pycache__',
  'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks',
  'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\numpy\\_pyinstaller'],
 {},
 [],
 [],
 False,
 False,
 False,
 {},
 '3.8.10 (tags/v3.8.10:3d8993a, May  3 2021, 11:48:03) [MSC v.1928 64 bit '
 '(AMD64)]',
 [('pyi_rth_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_subprocess.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('rename',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\rename.py',
   'PYSOURCE')],
 [('pkg_resources',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('uuid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\uuid.py',
   'PYMODULE'),
  ('hashlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pickle.py',
   'PYMODULE'),
  ('_compat_pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('struct',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\struct.py',
   'PYMODULE'),
  ('random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\random.py',
   'PYMODULE'),
  ('bisect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bisect.py',
   'PYMODULE'),
  ('ctypes.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('socket',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\selectors.py',
   'PYMODULE'),
  ('subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('contextlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('signal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\signal.py',
   'PYMODULE'),
  ('shutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('argparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\argparse.py',
   'PYMODULE'),
  ('gettext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\gettext.py',
   'PYMODULE'),
  ('gzip',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bz2.py',
   'PYMODULE'),
  ('pdb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pdb.py',
   'PYMODULE'),
  ('getopt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\getopt.py',
   'PYMODULE'),
  ('pydoc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\webbrowser.py',
   'PYMODULE'),
  ('email.message',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\base64.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\calendar.py',
   'PYMODULE'),
  ('email',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('quopri',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\quopri.py',
   'PYMODULE'),
  ('uu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\optparse.py',
   'PYMODULE'),
  ('http.server',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\server.py',
   'PYMODULE'),
  ('http',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('socketserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('ssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ssl.py',
   'PYMODULE'),
  ('html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tty.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('configparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\configparser.py',
   'PYMODULE'),
  ('csv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\csv.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('runpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\runpy.py',
   'PYMODULE'),
  ('shlex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\shlex.py',
   'PYMODULE'),
  ('glob',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\glob.py',
   'PYMODULE'),
  ('code',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\codeop.py',
   'PYMODULE'),
  ('__future__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\__future__.py',
   'PYMODULE'),
  ('dis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\opcode.py',
   'PYMODULE'),
  ('bdb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\cmd.py',
   'PYMODULE'),
  ('threading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('datetime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('pprint',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pprint.py',
   'PYMODULE'),
  ('copy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\copy.py',
   'PYMODULE'),
  ('string',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\string.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\typing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('distutils.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.debug',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.file_util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('py_compile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('_osx_support',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.spawn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.errors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_typing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('sysconfig',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\sysconfig.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('imp',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('inspect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\inspect.py',
   'PYMODULE'),
  ('ast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ast.py',
   'PYMODULE'),
  ('textwrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('tempfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tempfile.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('plistlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\plistlib.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('platform',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\platform.py',
   'PYMODULE'),
  ('pkgutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\zipfile.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\numbers.py',
   'PYMODULE'),
  ('hmac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_py_abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('getpass',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\netrc.py',
   'PYMODULE'),
  ('http.cookiejar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('stringprep',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('sqlalchemy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.pysybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\pysybase.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.pyodbc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('dataclasses',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('dummy_threading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util._compat_py3k',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\util\\_compat_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('asyncio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('greenlet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pypostgresql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pypostgresql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pygresql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pygresql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.oursql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\oursql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.mxodbc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\mxodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.mxodbc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\connectors\\mxodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.kinterbasdb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\kinterbasdb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.fdb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\fdb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.pool.dbapi_proxy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\pool\\dbapi_proxy.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.processors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util._preloaded',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\util\\_preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('paramiko',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\__init__.py',
   'PYMODULE'),
  ('paramiko.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\common.py',
   'PYMODULE'),
  ('paramiko.py3compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\py3compat.py',
   'PYMODULE'),
  ('paramiko.proxy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\proxy.py',
   'PYMODULE'),
  ('paramiko.config',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\config.py',
   'PYMODULE'),
  ('paramiko.hostkeys',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\hostkeys.py',
   'PYMODULE'),
  ('paramiko.pkey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\pkey.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x509',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x509.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x448',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x25519',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.poly1305',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\poly1305.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hmac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hashes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed448',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed25519',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dsa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs7.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.scrypt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\scrypt.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('ipaddress',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ipaddress.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('bcrypt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bcrypt.__about__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\bcrypt\\__about__.py',
   'PYMODULE'),
  ('six',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('paramiko.agent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\agent.py',
   'PYMODULE'),
  ('paramiko.file',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\file.py',
   'PYMODULE'),
  ('paramiko.packet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\packet.py',
   'PYMODULE'),
  ('paramiko.message',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\message.py',
   'PYMODULE'),
  ('paramiko.sftp_file',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\sftp_file.py',
   'PYMODULE'),
  ('paramiko.sftp_si',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\sftp_si.py',
   'PYMODULE'),
  ('paramiko.sftp_handle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\sftp_handle.py',
   'PYMODULE'),
  ('paramiko.sftp_attr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\sftp_attr.py',
   'PYMODULE'),
  ('paramiko.sftp_server',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\sftp_server.py',
   'PYMODULE'),
  ('paramiko.sftp_client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\sftp_client.py',
   'PYMODULE'),
  ('paramiko.sftp',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\sftp.py',
   'PYMODULE'),
  ('paramiko.ed25519key',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\ed25519key.py',
   'PYMODULE'),
  ('nacl.signing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\signing.py',
   'PYMODULE'),
  ('nacl.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\utils.py',
   'PYMODULE'),
  ('nacl.public',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\public.py',
   'PYMODULE'),
  ('nacl.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\exceptions.py',
   'PYMODULE'),
  ('nacl.encoding',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\encoding.py',
   'PYMODULE'),
  ('nacl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\__init__.py',
   'PYMODULE'),
  ('nacl.bindings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\__init__.py',
   'PYMODULE'),
  ('nacl.bindings.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\utils.py',
   'PYMODULE'),
  ('nacl.bindings.sodium_core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\sodium_core.py',
   'PYMODULE'),
  ('nacl.bindings.randombytes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\randombytes.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_sign',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_sign.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_shorthash',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_shorthash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretstream',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_secretstream.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretbox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_secretbox.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_scalarmult',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_scalarmult.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_pwhash',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_pwhash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_kx',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_kx.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_hash',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_hash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_generichash',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_generichash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_core.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_box',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_box.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_aead',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\bindings\\crypto_aead.py',
   'PYMODULE'),
  ('paramiko.ecdsakey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\ecdsakey.py',
   'PYMODULE'),
  ('paramiko.dsskey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\dsskey.py',
   'PYMODULE'),
  ('paramiko.ber',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\ber.py',
   'PYMODULE'),
  ('paramiko.rsakey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\rsakey.py',
   'PYMODULE'),
  ('paramiko.server',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\server.py',
   'PYMODULE'),
  ('paramiko.ssh_exception',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\ssh_exception.py',
   'PYMODULE'),
  ('paramiko.channel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\channel.py',
   'PYMODULE'),
  ('paramiko.buffered_pipe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\buffered_pipe.py',
   'PYMODULE'),
  ('paramiko.ssh_gss',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\ssh_gss.py',
   'PYMODULE'),
  ('paramiko.auth_handler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\auth_handler.py',
   'PYMODULE'),
  ('paramiko.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\client.py',
   'PYMODULE'),
  ('paramiko.win_openssh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\win_openssh.py',
   'PYMODULE'),
  ('paramiko.win_pageant',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\win_pageant.py',
   'PYMODULE'),
  ('paramiko._winapi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\_winapi.py',
   'PYMODULE'),
  ('paramiko.transport',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\transport.py',
   'PYMODULE'),
  ('paramiko.primes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\primes.py',
   'PYMODULE'),
  ('paramiko.kex_gss',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\kex_gss.py',
   'PYMODULE'),
  ('paramiko.kex_ecdh_nist',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\kex_ecdh_nist.py',
   'PYMODULE'),
  ('paramiko.kex_group16',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\kex_group16.py',
   'PYMODULE'),
  ('paramiko.kex_group14',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\kex_group14.py',
   'PYMODULE'),
  ('paramiko.kex_group1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\kex_group1.py',
   'PYMODULE'),
  ('paramiko.kex_gex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\kex_gex.py',
   'PYMODULE'),
  ('paramiko.kex_curve25519',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\kex_curve25519.py',
   'PYMODULE'),
  ('paramiko.compress',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\compress.py',
   'PYMODULE'),
  ('paramiko.pipe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\pipe.py',
   'PYMODULE'),
  ('paramiko.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\util.py',
   'PYMODULE'),
  ('paramiko._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\paramiko\\_version.py',
   'PYMODULE'),
  ('pandas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('numpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('doctest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\doctest.py',
   'PYMODULE'),
  ('difflib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\difflib.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('unittest.case',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('unittest.result',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.async_case',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.compat._pep440',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\compat\\_pep440.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('pandas._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util.testing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\util\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.series',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('jinja2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('markupsafe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('jinja2.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlwt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\excel\\_xlwt.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('cgi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\cgi.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.date_converters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\date_converters.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.util.version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.indexes.numeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('dateutil.parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.tz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('fractions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\fractions.py',
   'PYMODULE'),
  ('dateutil._common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.compat.chainmap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\compat\\chainmap.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.describe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\describe.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pytz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas._typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.io.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.plotting',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.api',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._libs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pymysql',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql.connections',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.charset',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.converters',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql._auth',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.times',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pymysql.err',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pymysql\\err.py',
   'PYMODULE')],
 [('VCRUNTIME140.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('python38.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\python38.dll',
   'BINARY'),
  ('libopenblas.FB5AE2TYXYH2IJRDKGDGQ3XBKLKTF43H.gfortran-win_amd64.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\.libs\\libopenblas.FB5AE2TYXYH2IJRDKGDGQ3XBKLKTF43H.gfortran-win_amd64.dll',
   'BINARY'),
  ('_hashlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('select',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('pyexpat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_multiprocessing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_decimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_queue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('unicodedata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('sqlalchemy.cimmutabledict',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\cimmutabledict.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('greenlet._greenlet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\greenlet\\_greenlet.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('sqlalchemy.cresultproxy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\cresultproxy.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy.cprocessors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\sqlalchemy\\cprocessors.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography.hazmat.bindings._openssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_openssl.pyd',
   'EXTENSION'),
  ('_cffi_backend',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography.hazmat.bindings._rust',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('bcrypt._bcrypt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('nacl._sodium',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\_sodium.pyd',
   'EXTENSION'),
  ('pandas._libs.writers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\writers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.window.indexers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.window.aggregations',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.vectorized',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.tzconversion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timezones',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timestamps',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timedeltas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.strptime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.period',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.parsing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.offsets',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.np_datetime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.nattype',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.fields',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.dtypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.conversion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.ccalendar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.testing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\testing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.sparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\sparse.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.reshape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\reshape.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.reduction',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\reduction.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.properties',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\properties.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.parsers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\parsers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.ops_dispatch',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.ops',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\ops.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.missing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\missing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\json.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.join',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\join.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.interval',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\interval.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.internals',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\internals.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.indexing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\indexing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.index',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\index.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.hashing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\hashing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.groupby',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\groupby.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.arrays',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\arrays.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.algos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\algos.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_tests',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_umath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.linalg.lapack_lite',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.mtrand',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._sfc64',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._philox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_philox.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._pcg64',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._mt19937',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.bit_generator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._generator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._bounded_integers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._common',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\random\\_common.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.fft._pocketfft_internal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.linalg._umath_linalg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe._speedups',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\markupsafe\\_speedups.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml.etree',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\etree.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml._elementpath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\_elementpath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.sax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\sax.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.objectify',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\objectify.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.html.diff',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\diff.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.html.clean',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\html\\clean.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.builder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\builder.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas.io.sas._sas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\sas\\_sas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\tslib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.lib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\lib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.hashtable',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\hashtable.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('libcrypto-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('sqlite3.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('python3.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\python3.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\msvcp140.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\vcruntime140_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\monthly_report\\build\\rename\\base_library.zip',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\WHEEL',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\WHEEL',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\LICENSE.APACHE',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\LICENSE.PSF',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\LICENSE.PSF',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\LICENSE.BSD',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\LICENSE.BSD',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('nacl\\py.typed',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\nacl\\py.typed',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\METADATA',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\METADATA',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\RECORD',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\RECORD',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('cryptography-38.0.1.dist-info\\LICENSE',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\cryptography-38.0.1.dist-info\\LICENSE',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA')],
 [])
