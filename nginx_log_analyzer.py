#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
from datetime import datetime
import argparse
import pandas as pd
import os
import glob


class NginxLogAnalyzer:
    def __init__(self, output_file):
        self.log_pattern = 'fs_96956_com_cn_https.access.log*'
        self.output_file = output_file.replace('.txt', '.xlsx')
        self.target_paths = ['/gcable_vote/getVoteGroupContent', '/gcable_vote/accountLogin','/gcable_vote/addVote', '/gcable_vote/logout']
        self.target_referrer = 'https://fs.96956.com.cn/goodVoice/'

    def parse_line(self, line):
        try:
            # 跳过空行
            if not line or line.isspace():
                return None
            
            # 尝试解析JSON
            try:
                log_data = json.loads(line)
            except json.JSONDecodeError:
                print(f"JSON解析错误，非法JSON格式: {line[:200]}...")
                return None
            
            # 获取字段，使用空字符串作为默认值
            fields = log_data.get('@fields', {})
            current_path = fields.get('url', '')
            referrer = fields.get('http_referrer', '')
            
            # 尝试解析request_body，如果失败则使用空字典
            try:
                request_body = json.loads(fields.get('request_body', '{}'))
            except (json.JSONDecodeError, TypeError):
                request_body = {}
            
            # 检查路径和referrer
            if current_path in self.target_paths and self.target_referrer in referrer:
                return {
                    'timestamp': log_data.get('@timestamp', ''),
                    'ip': fields.get('remote_addr', ''),
                    'path': current_path,
                    'status': fields.get('status', ''),
                    'referrer': referrer,
                    'phone': request_body.get('phone', ''),
                    'user_agent': fields.get('http_user_agent', ''),
                    'contestant_id': request_body.get('contestantId', '')
                }
        except Exception as e:
            print(f"解析错误: {str(e)}")
            print(f"错误日志行: {line[:200]}...")
        return None

    def read_file_with_fallback_encoding(self, file_path):
        encodings = ['utf-8', 'gbk', 'gb2312', 'iso-8859-1', 'latin1']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.readlines()
            except UnicodeDecodeError:
                continue
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return f.readlines()

    def analyze(self):
        all_matched_logs = []
        file_logs = {}
        total_lines = 0
        processed_files = 0
        error_lines = 0
        try:
            log_files = glob.glob(self.log_pattern)
            if not log_files:
                print("错误：未找到匹配的日志文件")
                return
            print(f"找到 {len(log_files)} 个日志文件")
            for log_file in log_files:
                processed_files += 1
                file_name = os.path.basename(log_file)
                print(f"\n处理文件 ({processed_files}/{len(log_files)}): {file_name}")
                file_matched_logs = []
                try:
                    lines = self.read_file_with_fallback_encoding(log_file)
                    for line_num, line in enumerate(lines, 1):
                        total_lines += 1
                        try:
                            parsed_log = self.parse_line(line.strip())
                            if parsed_log:
                                parsed_log['_log_file'] = file_name
                                file_matched_logs.append(parsed_log)
                                all_matched_logs.append(parsed_log)
                        except Exception as e:
                            error_lines += 1
                            print(f"文件 {file_name} 第 {line_num} 行解析错误:")
                            print(f"错误信息: {str(e)}")
                            print(f"日志内容: {line[:200]}...")
                            continue
                        if total_lines % 1000 == 0:
                            print(f"已处理 {total_lines} 行，找到 {len(all_matched_logs)} 条匹配记录")
                except Exception as e:
                    print(f"处理文件 {file_name} 时出错: {str(e)}")
                    continue
                if file_matched_logs:
                    file_logs[file_name] = file_matched_logs
            if not all_matched_logs:
                print("未找到匹配的记录")
                return
            columns_map = {
                'timestamp': '请求时间',
                'ip': '请求IP',
                'path': '请求路径',
                'status': '状态码',
                'phone': '手机号',
                'contestant_id': '参赛者ID',
                'user_agent': '用户代理',
                '_log_file': '日志文件'
            }
            with pd.ExcelWriter(self.output_file, engine='openpyxl') as writer:
                all_df = pd.DataFrame(all_matched_logs)
                all_df = all_df.rename(columns=columns_map)
                all_df.to_excel(writer, sheet_name='全部日志', index=False)
                for file_name, logs in file_logs.items():
                    sheet_name = file_name[-8:]
                    df = pd.DataFrame(logs)
                    df = df.rename(columns=columns_map)
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                for sheet_name in writer.sheets:
                    worksheet = writer.sheets[sheet_name]
                    for column in worksheet.columns:
                        max_length = 0
                        column = [cell for cell in column]
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = (max_length + 2)
                        worksheet.column_dimensions[column[0].column_letter].width = adjusted_width
            print(f"\n分析完成！")
            print(f"处理了 {len(log_files)} 个日志文件")
            print(f"总共处理 {total_lines} 行日志")
            print(f"跳过 {error_lines} 行错误日志")
            print(f"找到 {len(all_matched_logs)} 条匹配记录")
            for file_name, logs in file_logs.items():
                print(f"{file_name[-8:]}: {len(logs)} 条记录")
            print(f"结果已保存到 {self.output_file}")
        except Exception as e:
            print(f"处理过程中发生错误：{str(e)}")


def main():
    parser = argparse.ArgumentParser(description='Nginx JSON日志分析工具')
    parser.add_argument('--output', '-o', default='output.xlsx', help='输出文件路径')
    args = parser.parse_args()
    analyzer = NginxLogAnalyzer(args.output)
    analyzer.analyze()


if __name__ == '__main__':
    main()