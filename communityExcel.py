# -*- coding: UTF-8 -*-
import datetime
import random
import xlrd2 as xlrd
import pymysql

db = pymysql.connect(
    host="127.0.0.1",
    port=13306,
    user="root",
    password="FScatv@2016",
    database="gcable_community_dev")
cursor = db.cursor()



# 导入需要读取的第一个Excel表格的路径


data2 = xlrd.open_workbook(r'2.xlsx')
table = data2.sheets()[0]

def getdata():
    data1 = xlrd.open_workbook(r'1.xlsx')
    table = data1.sheets()[0]
    data1.release_resources()
    datamap = {}
    for rown in range(table.nrows-1):
        # for rown in range(1):

        # print(table.cell_value(rown + 1, 6))
        # print(table.cell_value(rown + 1, 17))
        datamap[str(table.cell_value(rown + 1, 17))]=table.cell_value(rown + 1, 6)+','+table.cell_value(rown + 1, 18)
    return datamap


def import_excel(excel):
    datamap = getdata()

        # 写入sp表

    for rown in range(excel.nrows-1):
        # print(table.cell_value(rown + 1, 0))
        # print(table.cell_value(rown + 1, 1))
        # print(table.cell_value(rown + 1, 4))
        # print(table.cell_value(rown + 1, 6))
        try:
            itemsName=str(table.cell_value(rown + 1, 0))
            taxCode=str(datamap.get(itemsName)).split(',')[0]
            inviceUnit=str(datamap.get(itemsName)).split(',')[1]
            taxRate=str(table.cell_value(rown + 1, 4)/100)
            businessScope=str(table.cell_value(rown , 1))
            taxProductName=str(table.cell_value(rown + 1, 6))

            sql = "UPDATE t_community_pay_items SET inviceInfo = '{\"taxRate\":\""+taxRate+"\",\"taxCode\":\""+taxCode+"\",\"businessScope\":\""+businessScope+"\",\"taxProductName\":\""+taxProductName+"\",\"inviceUnit\":\""+inviceUnit+"\"}' WHERE itemsName = '"+itemsName+"'"

            cursor.execute(sql)

            db.commit()
        except:
            # print("error------------------------------")
            print(itemsName)
            # print("error------------------------------")


def update_excel(excel):
    for rown in range(excel.nrows-1):
    # for rown in range(1):

        id = "%d" % table.cell_value(rown + 1, 0)
        flbm ="%d" % table.cell_value(rown + 1, 1)
        print(id)
        print(flbm)
        print(table.cell_value(rown + 1, 2))



        # 写入sp表
        sql = "UPDATE t_product_specification set commodityClassificationCode = "+ flbm +" where id = "+ id  # 注意是%s,不是s%

        cursor.execute(sql)
        db.commit()





import_excel(table)

cursor.close()
db.close()

