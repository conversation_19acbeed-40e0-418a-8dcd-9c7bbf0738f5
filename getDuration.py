import cv2
import os


def video_duration(dir_name):
    sum_duration = 0
    filenames = []
    durations = []
    for root, dirs, files in os.walk(dir_name, topdown=False):
        # dir_name = os.getcwd()
        for filename in files:
            if filename.endswith('.ts') or filename.endswith('.mp4') or filename.endswith('.MP4') or filename.endswith('.mov'):
                cap = cv2.VideoCapture(dir_name + "\\" + filename)
                if cap.isOpened():
                    rate = cap.get(5)
                    frame_num = cap.get(7)
                    duration = frame_num / rate
                    filenames.append(filename.replace('.ts', ''))

                    m, s = divmod(duration, 60)
                    h, m = divmod(m, 60)
                    duration = ("%d:%02d:%02d" % (h, m, s))

                    durations.append(duration)
    write_excel(filenames, durations)
    return sum_duration


import openpyxl

def write_excel(filename,duration):
    workbook = openpyxl.load_workbook(r'媒资制作室发布单.xlsx')

    sheet = workbook["发布单"]

    for row in range(0, len(filename)):

        sheet.cell(row + 3, 3).value = filename[row]  # 写文件
        sheet.cell(row + 3, 6).value = duration[row]
    workbook.save(r'媒资制作室发布单1.xlsx')  # 一定要记得保存


if __name__ == '__main__':
    file = os.getcwd()
    total_video_time = video_duration(file)
    print(total_video_time)
    print(f"{file} 目录下全部视频总时长为：{total_video_time}秒")
