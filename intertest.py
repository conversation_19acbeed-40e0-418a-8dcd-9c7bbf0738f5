import json

import requests
import time
import hashlib

# url = 'http://bossapitest.gdtvdv.com/api/BossBaseApi/QueProductByCatalog'
# url = "http://bossapitest.gdtvdv.com/api/BossBaseApi/QueCustInfo"

url = "http://bossapitest.gdtvdv.com/api/BossBaseApi/BizPreproess"

headers = {
    'Content-Type': 'application/json'
}

timestamp = int(round(time.time() * 1000))


def get_user_info(identifier, identifierType):
    url = "http://localhost:9999/api/BossBaseApi/QueCustInfo"
    headers = {
        'Content-Type': 'application/json'
    }

    body = {
        "key": "bfss8e9ve8jm",
        "requestContent": {
            "queCustInfoData": {
                "city": "FS",
                "identifier": identifier,
                "identifierType": identifierType,
                "pagesize": "10"
            },
        }, "timestamp": timestamp}

    # body = {"key": "fmsv4m7zktqu",
    #         "requestContent": {"citycode": "FS", "data": {"catalogid": "380101"}}, "timestamp": 1630569533767}

    print(sorted({"identifier", "city", "pagesize", "identifierType"}))

    # 目标md5串
    str_parm = ''
    # 将字典中的key排序
    for p in sorted(body):
        # 每次排完序的加到串中
        # str类型需要转化为url编码格式
        if p == 'requestContent':
            str_parm = str_parm + str(json.dumps(body[p], separators=(',', ':')) + '|')
        else:
            str_parm = str_parm + str(body[p]) + "|"
    # str_parm = str_parm + str(body[p]) + "&"
    # 加上对应的key
    str_parm = str_parm + '280w0jtqkilixvtvccbccnc7awhabciw'
    str_parm.replace(" ", "")
    str_parm.replace("\n", "").replace('\r', '')
    str_parm.replace('\'', '\"')
    print(str_parm)
    # 转换md5串
    if isinstance(str_parm, str):
        # 如果是unicode先转utf-8
        parmStr = str_parm.encode("utf-8")
        m = hashlib.md5()
        m.update(parmStr)
    body["dataSign"] = m.hexdigest().upper()
    body = json.dumps(body)
    print(body)
    res = requests.post(url, data=body, headers=headers)
    print(res.status_code)
    res = json.loads(res.text)
    print(res)
    for i in res['data']['output']['custs']:
        print(i['custid'])


def get_product(catalogid):
    url = "http://localhost:9999/api/BossBaseApi/QuePKGInfo"
    headers = {
        'Content-Type': 'application/json'
    }

    body = {
        "key": "bfss8e9ve8jm",
        "requestContent": {
                "pkgcode": catalogid
        }, "timestamp": timestamp}

    # body = {"key": "fmsv4m7zktqu",
    #         "requestContent": {"citycode": "FS", "data": {"catalogid": "380101"}}, "timestamp": 1630569533767}

    print(sorted({"identifier", "city", "pagesize", "identifierType"}))

    # 目标md5串
    str_parm = ''
    # 将字典中的key排序
    for p in sorted(body):
        # 每次排完序的加到串中
        # str类型需要转化为url编码格式
        if p == 'requestContent':
            str_parm = str_parm + str(json.dumps(body[p], separators=(',', ':')) + '|')
        else:
            str_parm = str_parm + str(body[p]) + "|"
    # str_parm = str_parm + str(body[p]) + "&"
    # 加上对应的key
    str_parm = str_parm + '280w0jtqkilixvtvccbccnc7awhabciw'
    str_parm.replace(" ", "")
    str_parm.replace("\n", "").replace('\r', '')
    str_parm.replace('\'', '\"')
    print(str_parm)
    # 转换md5串
    if isinstance(str_parm, str):
        # 如果是unicode先转utf-8
        parmStr = str_parm.encode("utf-8")
        m = hashlib.md5()
        m.update(parmStr)
    body["dataSign"] = m.hexdigest().upper()
    body = json.dumps(body)
    print(body)
    res = requests.post(url, data=body, headers=headers)
    print(res.status_code)
    res = json.loads(res.text)
    print(res)
    for i in res['data']['output']['custs']:
        print(i['custid'])

# get_product("0008HP")


def confirm(orderid):
    url = "http://bossapitest.gdtvdv.com/api/BossBaseApi/BizOrderCommit"
    headers = {
        'Content-Type': 'application/json'
    }
    body = {
        "key": "fmsv4m7zktqu",
        "timestamp": timestamp,
        "requestContent": {
            "citycode": "FS",
            "data": {
                "orderid": orderid,
                "paycode": "030000",
                "payreqid": orderid,
                "payway": "22"
            }
        }
    }

    # 目标md5串
    str_parm = ''
    # 将字典中的key排序
    for p in sorted(body):
        # 每次排完序的加到串中
        # str类型需要转化为url编码格式
        if p == 'requestContent':
            str_parm = str_parm + str(json.dumps(body[p], separators=(',', ':')) + '|')
        else:
            str_parm = str_parm + str(body[p]) + "|"
    # str_parm = str_parm + str(body[p]) + "&"
    # 加上对应的key
    str_parm = str_parm + 'uj4pdvz492ow3flgjwhoxs022x3cbcmq'
    str_parm.replace(" ", "")
    str_parm.replace("\n", "").replace('\r', '')
    str_parm.replace('\'', '\"')
    # 转换md5串
    if isinstance(str_parm, str):
        # 如果是unicode先转utf-8
        parmStr = str_parm.encode("utf-8")
        m = hashlib.md5()
        m.update(parmStr)
    body["dataSign"] = m.hexdigest().upper()
    body = json.dumps(body)
    print(body)
    res = requests.post(url, data=body, headers=headers)
    print(res.status_code)
    res = json.loads(res.text)
    print(res)


def order(custid, keyno):
    url = "http://bossapitest.gdtvdv.com/api/BossBaseApi/BizPreproess"
    headers = {
        'Content-Type': 'application/json'
    }
    body = {
        "key": "fmsv4m7zktqu",
        "timestamp": timestamp,
        "requestContent": {
            "citycode": "FS",
            "data": {
                "custid": custid,
                "iscrtorder": "Y",
                "orderparams": [{
                    "count": "1",
                    "ispostpone": "Y",
                    "keyno": keyno,
                    "ordertype": "3",
                    "permark": "1",
                    "salescode": "SJDSC000055",
                    "unit": "1"}]
            }
        }
    }

    print(sorted({"permark", "ordertype", "salescode", "count", "unit", "ispostpone", "keyno"}))
    print(sorted({"key", "timestamp", "requestContent"}))
    print(sorted({"data", "citycode"}))

    # 目标md5串
    str_parm = ''
    # 将字典中的key排序
    for p in sorted(body):
        # 每次排完序的加到串中
        # str类型需要转化为url编码格式
        if p == 'requestContent':
            str_parm = str_parm + str(json.dumps(body[p], separators=(',', ':')) + '|')
        else:
            str_parm = str_parm + str(body[p]) + "|"
    # str_parm = str_parm + str(body[p]) + "&"
    # 加上对应的key
    str_parm = str_parm + 'uj4pdvz492ow3flgjwhoxs022x3cbcmq'
    str_parm.replace(" ", "")
    str_parm.replace("\n", "").replace('\r', '')
    str_parm.replace('\'', '\"')
    # 转换md5串
    if isinstance(str_parm, str):
        # 如果是unicode先转utf-8
        parmStr = str_parm.encode("utf-8")
        m = hashlib.md5()
        m.update(parmStr)
    body["dataSign"] = m.hexdigest().upper()
    body = json.dumps(body)
    print(body)
    res = requests.post(url, data=body, headers=headers)
    print(res.status_code)
    res = json.loads(res.text)
    print(res)

get_user_info("15622321650","1")
# order("6901285129", "8757003702401458")
# confirm("75758369")
# body = {
#     "key": "fmsv4m7zktqu",
#     "timestamp": timestamp,
#     "requestContent": {
#         "salesid": 6601981
#     }
# }


# body = {
#     "key": "fmsv4m7zktqu",
#     "timestamp": 1629701581863,
#     "requestContent": {
#         "catalogid": 380101,
#         "city":"FS"
#     }
# }


# # 目标md5串
# str_parm = ''
# # 将字典中的key排序
# for p in sorted(body):
#     # 每次排完序的加到串中
#     # str类型需要转化为url编码格式
#     if p == 'requestContent':
#         str_parm = str_parm + str(json.dumps(body[p], separators=(',', ':')) + '|')
#     else:
#         str_parm = str_parm + str(body[p]) + "|"
# # str_parm = str_parm + str(body[p]) + "&"
# # 加上对应的key
# str_parm = str_parm + 'uj4pdvz492ow3flgjwhoxs022x3cbcmq'
# str_parm.replace(" ", "")
# str_parm.replace("\n", "").replace('\r', '')
# str_parm.replace('\'', '\"')
# print('md5字符串:' + str_parm)
# # 转换md5串
# if isinstance(str_parm, str):
#     # 如果是unicode先转utf-8
#     parmStr = str_parm.encode("utf-8")
#     m = hashlib.md5()
#     m.update(parmStr)
#     print('md5:' + m.hexdigest().upper())
#
# body["dataSign"] = m.hexdigest().upper()
# body = json.dumps(body)
# print(body)
# res = requests.post(url, data=body, headers=headers)
# print(res.status_code)
# print(res.text)
def get_test():
    url = "http://yl.fsvkool.top:8088/index/map/agent_detail?id=101"
    headers = {
        # 'Content-Type': 'application/json'
    }

    # body = {
    #     "key": "bfss8e9ve8jm",
    #     "requestContent": {
    #         "queCustInfoData": {
    #             "city": "FS",
    #             "identifier": identifier,
    #             "identifierType": identifierType,
    #             "pagesize": "10"
    #         },
    #     }, "timestamp": timestamp}

    # body = {"key": "fmsv4m7zktqu",
    #         "requestContent": {"citycode": "FS", "data": {"catalogid": "380101"}}, "timestamp": 1630569533767}


    res = requests.get(url)
    print(res.status_code)
    # res = json.loads(res.text)
    print(res.text)

# get_test()