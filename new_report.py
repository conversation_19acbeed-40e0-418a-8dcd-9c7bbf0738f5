# -*- coding: UTF-8 -*-
import pymysql
import requests
import json
import datetime
from dateutil.relativedelta import relativedelta
from bs4 import BeautifulSoup
import time

today = datetime.date.today()


def get_pic():
    for i in range(0, 54):
        list =  [1403, 1404, 1405, 1587, 1049, 1574, 1337, 1339, 1341, 1588, 1053, 1062, 1408, 1409, 1412, 1589, 1081,
                1573, 1447, 1448, 1449, 1591,
                1129, 1581, 1453, 1454, 1455, 1592, 1145, 1582, 1489, 1490, 1491, 1595, 1044, 1577, 1495, 1496, 1497,
                1596, 1193, 1578, 1501,
                1502, 1503, 1597, 1219, 1579, 1508, 1512, 1513, 1598, 1235, 1580]

        headers = {
            'Referer': 'http://*************/cacti/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36',
            'Upgrade-Insecure-Requests': '1',
            'Host': '*************',
            'DNT': '1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        }

        url = 'http://*************/cacti/'
        res = requests.get(url, headers=headers)
        bs = BeautifulSoup(res.content, 'lxml')
        tem = bs.find(attrs={'name': '__csrf_magic'})
        cookies = requests.utils.dict_from_cookiejar(res.cookies)

        body = {
            'action': 'login',
            '__csrf_magic': tem.get('value'),
            'login_username': 'fscatv',
            'login_password': 'Cacti#2303'
        }
        res = requests.post(url, headers=headers, data=body, cookies=cookies)
        # print(res.cookies)
        # cookies = requests.utils.dict_from_cookiejar(res.cookies)
        # print(cookies)
        # cookies = 'Cacti='+cookies.get('Cacti')
        # print(cookies)
        # print(res.content)

        # 字符类型的时间
        first_day = str(datetime.date(today.year, today.month, 1) - relativedelta(months=1)) + " 00:00:00"
        last_day = str(datetime.date(today.year, today.month + 0, 1) - relativedelta(days=1, months=0)) + " 23:59:59"

        first_day = '2024-04-24 00:00:00'
        last_day = '2025-04-25 23:59:59'
        print(first_day)
        print(last_day)
        # 1.转为时间数组
        first_day = time.strptime(first_day, "%Y-%m-%d %H:%M:%S")
        last_day = time.strptime(last_day, "%Y-%m-%d %H:%M:%S")
        # time.struct_time(tm_year=2018, tm_mon=12, tm_mday=12, tm_hour=17, tm_min=23, tm_sec=50, tm_wday=2, tm_yday=346, tm_isdst=-1)

        # 2.转为时间戳
        start = int(time.mktime(first_day))
        end = int(time.mktime(last_day))

        headers = {
            'Referer': 'http://*************/cacti/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36',
            'Upgrade-Insecure-Requests': '1',
            'Host': '*************',
            'DNT': '1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        }

        pic_url = 'http://*************/cacti/graph_image.php?local_graph_id=' + str(list[
                                                                                         i]) + '&rra_id=0&graph_height=120&graph_width=500&title_font_size=10&view_type=tree&graph_start=' + str(
            start) + '&graph_end=' + str(end)
        print(pic_url)
        res = requests.get(pic_url, headers=headers, cookies=cookies)
        with open('./template/Graphs __ Tree Mode/pic_' + str(i) + '.jpg', 'wb') as f:
            f.write(res.content)
            print(res.content)


# get_pic()

# url = "http://*************:8180/hdBoData/getIepgAssetFileDescribe"
# payload="{}"
# headers = {
#   'Content-Type': 'application/json'
# }
# response = requests.request("POST", url, headers=headers, data=payload)
# print(response.text)
# data = json.loads(response.text)
# zongshipin = data.get('assetsQuantity')
# zongrongliang =  "%.2f" % data.get('assetsDuration')
#
# print(zongrongliang)


#####本年第一天
first_day_of_the_year = datetime.date.today().replace(month=1).replace(day=1)
first_day_of_the_year = str(first_day_of_the_year)
#####今天
today = datetime.date.today().replace(month=1,day=2)

shuju = []
zongliang = 0

if 1 == today.month:
    month_count = 13
else:
    month_count = today.month
for i in range(1, month_count):
    print(i)
    if today.month == 1:
        first_day = str(datetime.date(today.year, i, 1) - relativedelta(years=1))
        if i == 12:
            last_day = str(datetime.date(today.year, 1, 1) - relativedelta(days=1))
        else:
            last_day = str(datetime.date(today.year, i + 1, 1) - relativedelta(days=1, years=1))
        print(first_day)
        print(last_day)
    else:
        first_day = str(datetime.date(today.year, i, 1))
        # last_day=str(datetime.date(today.year,i+1,1) - relativedelta(days=1))
        if i == 12:
            last_day = str(datetime.date(today.year, 1, 1) - relativedelta(days=1))
        else:
            last_day = str(datetime.date(today.year, i + 1, 1) - relativedelta(days=1))
        print(first_day)
        print(last_day)


    # first_day = '2022-10-01 00:00:00'
    # last_day = '2022-10-31 23:59:59'     #要加年月日
    data = requests.get(
        'http://172.18.254.215/vote/getLibraryTotalVisit?jsonData={"startDate":"' + first_day + ' 00:00:00","endDate":"' + last_day + ' 23:59:59"}')
    data = json.loads(data.content)
    print(str(i) + '月总访问量：' + str(data['totalVisit']))
    zongfangwen = str(data['totalVisit'])
    # print(str(i)+'月电视用户数：' + str(data['tvUserCount']))
    dianshiyonghu = str(data['tvUserCount'])
    # print(str(i)+'月佛图用户数：' + str(data['libraryUserCount']) + '\n')
    fotuyonghu = str(data['libraryUserCount'])

    zongliang = zongliang + data['totalVisit']
    temp = [str(data['totalVisit']), str(data['tvUserCount']), str(data['libraryUserCount']), str(zongliang)]
    shuju.append(temp)

now_time = datetime.datetime.now()
if (1 == now_time.month):
    year = str(now_time.year - 1)
    month = str(now_time.month + 11)
else:
    year = str(now_time.year)
    month = str(now_time.month)
db = pymysql.connect(
    host="127.0.0.1",
    port=3326,
    user="root",
    password="FScatv@2016",
    database="newfaith")
cursor = db.cursor()
cursor.execute(
    "SELECT COUNT(*) FROM t_upshelf_column a LEFT JOIN t_column b ON a.`columnId`=b.`id` WHERE b.`serviceId`=5 AND SUBSTR(a.`upshelfTime`,1,4)='" + year + "'")
gelei = cursor.fetchone()
print('各类信息：%s' % gelei)
gelei = '%s' % gelei
cursor.execute(
    "select count(*) from t_library_asset where examineStatus=1 and substr(operationTime,1,4)='" + year + "'")
xinzeng = cursor.fetchone()
print('新增视频：%s' % xinzeng)
xinzeng = '%s' % xinzeng
cursor.execute(
    "SELECT COUNT(*) FROM t_upshelf_column a LEFT JOIN t_column b ON a.`columnId`=b.`id` WHERE b.`serviceId`=5 AND SUBSTR(a.`upshelfTime`,1,4)='" + year + "' AND b.`id`='955'")
xinshu = cursor.fetchone()
print('新书推荐：%s' % xinshu)
xinshu = '%s' % xinshu
db.close()

import docxtpl
from docx.shared import Mm, Inches, Pt
import jinja2

tpl = docxtpl.DocxTemplate('C:/Users/<USER>/Desktop/monthly_report/template/test.docx')
context = {}

for i in range(0, 54):
    context.setdefault('image' + str(i), docxtpl.InlineImage(tpl,
                                                             'C:/Users/<USER>/Desktop/monthly_report/template/Graphs __ Tree Mode/pic_' + str(
                                                                 i) + '.jpg', width=Mm(150)))

for i in range(0, len(shuju)):
    context.setdefault('fangwen' + str(i + 1), shuju[i][0])
    context.setdefault('dianshi' + str(i + 1), shuju[i][1])
    context.setdefault('yonghu' + str(i + 1), shuju[i][2])
    context.setdefault('leiji' + str(i + 1), shuju[i][3])

context.setdefault('time', first_day + "——" + last_day)
context.setdefault('year', today.year)
context.setdefault('month', today.month - 1)

context.setdefault('s', shuju[-1][0])
context.setdefault('z', shuju[-1][3])
context.setdefault('gelei', gelei)
context.setdefault('xinzeng', xinzeng)
context.setdefault('xinshu', xinshu)
# context.setdefault('zongrongliang', zongrongliang)
# context.setdefault('zongshipin', zongshipin)
jinja_env = jinja2.Environment(autoescape=True)
tpl.render(context, jinja_env)
tpl.save('C:/Users/<USER>/Desktop/monthly_report/月报' + '.docx')
print('报告已生成')

lmonth = today - relativedelta(months=1)  # 上个月
lwmonth = today - relativedelta(months=2)  # 上上个月
first_day_of_the_month_before_last = str(datetime.date(lwmonth.year, lwmonth.month, 1))  # 上上月第一天
last_day_of_the_month_before_last = str(datetime.date(lmonth.year, lmonth.month, 1) - relativedelta(days=1))  # 上上月最后一天

#
# now_time = datetime.datetime.now()
# if(1==now_time.month):
#     year=str(now_time.year-1)
#     month=str(now_time.month+11)
# else:
#     year = str(now_time.year)
#     month=str(now_time.month)
#
# first_day_of_last_month = str((datetime.date.today().replace(day=1) - datetime.timedelta(1)).replace(day=1))
# last_day_of_last_month = str((datetime.date.today().replace(day=1) - datetime.timedelta(days=1)))
# print(first_day_of_the_month_before_last,last_day_of_the_month_before_last,first_day_of_last_month,last_day_of_last_month)
#
#
# data = requests.get('http://**************:8080/vote/getLibraryTotalVisit?jsonData={"startDate":"'+first_day_of_the_year+'","endDate":"'+last_day_of_last_month+'"}')
# data = json.loads(data.content)
# print('年总访问量：'+str(data['totalVisit']))
# nianzongfangwen=str(data['totalVisit'])
#
#
#
#
#
#
#
# ################################################################################################
# ################################################################################################
# db = pymysql.connect(
#     host="127.0.0.1",
#     port=3326,
#     user="root",
#     password="FScatv@2016",
#     database="newfaith")
# cursor = db.cursor()
# cursor.execute("SELECT COUNT(*) FROM t_upshelf_column a LEFT JOIN t_column b ON a.`columnId`=b.`id` WHERE b.`serviceId`=5 AND SUBSTR(a.`upshelfTime`,1,4)='"+year+"'")
# gelei = cursor.fetchone()
# print('各类信息：%s' % gelei)
# gelei='%s' % gelei
# cursor.execute("select count(*) from t_library_asset where examineStatus=1 and substr(operationTime,1,4)='"+year+"'")
# xinzeng = cursor.fetchone()
# print('新增视频：%s' % xinzeng)
# xinzeng='%s' % xinzeng
# cursor.execute("SELECT COUNT(*) FROM t_upshelf_column a LEFT JOIN t_column b ON a.`columnId`=b.`id` WHERE b.`serviceId`=5 AND SUBSTR(a.`upshelfTime`,1,4)='"+year+"' AND b.`id`='955'")
# xinshu = cursor.fetchone()
# print('新书推荐：%s' % xinshu)
# xinshu='%s' % xinshu
# db.close()
