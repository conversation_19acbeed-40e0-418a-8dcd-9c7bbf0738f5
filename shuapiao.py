import json
import random
import requests
import time
import datetime
from concurrent.futures import ThreadPoolExecutor


def shuapiao(service_id):
    region_id = str(random.randint(1600, 1799))

    url = "http://**************/vote/reportBehavior?jsonData={\"serviceId\": 2, 	\"folderId\": \"825\", 	\"groupId\": null, 	\"smartcardId\": \"8757008888888888\", 	\"ipAddr\": \"************\", 	\"macAddr\": \"00-00-00-00-00\", 	\"phone\": \"\", 	\"userCode\": \"760088888888\", 	\"regionCode\": \"" + region_id + "\", 	\"fromAddr\": \"FS_PORTAL\", 	\"currentAddr\": \"首页|首页|\", 	\"toAddr\": \"\", 	\"toAddrType\": 1, 	\"deviceType\": 2 }"
    headers = {
        'Content-Type': 'application/json'
    }

    # print(service_id)
    # print(region_id)
    res = requests.get(url, headers=headers)
    res = json.loads(res.text)
    print(res)


def main():
    """
    主函数
    :return:
    """
    today = datetime.date.today()
    date = time.strftime("%Y-%m-%d %H:%M:%S")
    day = time.strftime("%d")
    hour = time.strftime("%H")
    minute = time.strftime("%M")
    second = time.strftime("%S")
    print("输入id:")
    # ids = input()
    ids = [2]
    # hours = [18]
    minutes = [3]
    start_time = time.time()
    count = 0
    # while(int(time.strftime("%d")) == 25):
    while (True):
        try:
            date = time.strftime("%Y-%m-%d %H:%M:%S")
            if int(hour) == 0:
                count = 0
            hour = time.strftime("%H")
            minute = time.strftime("%M")
            # print(str(hour)+"   "+str(minute))
            threadPool = ThreadPoolExecutor(max_workers=4, thread_name_prefix="shuapiao_")
            if 9 <= int(hour) <= 17:
                # threadPool = ThreadPoolExecutor(max_workers=4, thread_name_prefix="shuapiao_")
                for i in range(1):
                    future = threadPool.submit(shuapiao(ids[random.randint(0, len(ids) - 1)]))
                    time.sleep(2)
                    count += 1
                    print(date + " 刷票数：" + str(count))
                # threadPool.shutdown(wait=True)
            else:
                for i in range(1):
                    future = threadPool.submit(shuapiao(ids[random.randint(0, len(ids) - 1)]))
                    time.sleep(11)
                    count += 1
                    print(date + " 刷票数：" + str(count))
        except Exception as e:
            print(e)
        threadPool.shutdown(wait=True)
    end_time = time.time()


if __name__ == '__main__':
    main()
