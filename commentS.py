#-*- coding: UTF-8 -*-
import requests
from bs4 import BeautifulSoup
import lxml
import json
import time
import requests

headers = {
    'authority': 'live.weixin.qq.com',
    'sec-ch-ua': '^\\^Google',
    'accept': 'application/json, text/plain, */*',
    'dnt': '1',
    'sec-ch-ua-mobile': '?0',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'referer': 'https://live.weixin.qq.com/livemp/broadcast/control/53?token=1538867467^&lang=zh_CN',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
    'cookie': 'pgv_info=ssid=s7211604248; pgv_pvid=5856515978; wxuin=10956862149567; ua_id=wNVZExn1CBFYl7BUAAAAAKt7esNrpgne7eLcMOVp_Mg=; openid=o-agI0R31oTrgbYpfFKCyqhPA9wg; data_bizuin=3507005689; slave_user=gh_4632b03820d2; version=1; uuid=75844b9b7721548e294af061e5524aa5; slave_sid=eDRGNmlVVmFIOWxDbmczYUZweGt2YnRPRVVoX3JYNm5vSnQ2NkdMZEl5ZjY0TElOcmdWYmJhOFhQTEZXVXVrQndDRzRBdU5JQWhVdjRpUG1qc1BpV0JKOEJGU01YQWJYam9UVmhDQjBvNVFIOGJLNm40UE8yMlpaZXZsampJeGpLWDBLdDQ3cWVKemxxbmNn; token=2035862992; rand_info=CAESIIEGGx10eKpKqXX/9mWxt+Z6QZ86kcteWoCdpRTcT6Gq',
}

params = (
    ('roomId', '53'),
    ('token', '2035862992'),
    ('lang', 'zh_CN'),
    ('random', '0.3300930843607641'),
)

response = requests.get('https://live.weixin.qq.com/livemp/cgi/broadcast/getstat', headers=headers, params=params)
print(response.text)
data=json.loads(response.text)
# string = '{"ret":0,"success":true,"openid":"o-agI0R31oTrgbYpfFKCyqhPA9wg","watchData":{"watchUv":1185,"watchPv":39844,"averageWatchTime":526,"totalWatchTime":624457,"maxOnlineUv":155,"onlineUv":0,"totalWatchTimeVer_2":{"low":0,"high":0,"unsigned":true}},"interactionData":{"shareUv":139,"sharePv":339,"likeUv":234,"likePv":47799,"commentUv":307,"commentPv":3864},"goodsData":{"pushCnt":50,"clickUv":240,"clickPv":1525},"reportData":{"reportCommentPv":1,"reportCommentUv":1,"reportLiveRoomPv":0,"reportLiveRoomUv":0},"subscribeData":{"subscribeUv":173,"subscribePv":192,"unsubscribeUv":34,"unsubscribePv":47}}'
# data = json.loads(string)
str = '''
'峰值在线' '''+str(data['watchData']['maxOnlineUv'])+'''
'实时在线' '''+str(data['watchData']['onlineUv'])+'''
'观看人数' '''+str(data['watchData']['watchUv'])+'''
'观看次数'   '''+str(data['watchData']['watchPv'])+'''
'平均观看时长' '''+str(data['watchData']['averageWatchTime'])+'''
'累计商品推送次数'  '''+str(data['goodsData']['pushCnt'])+'''
'商品点击人数'  '''+str(data['goodsData']['clickUv'])+'''
'商品点击次数'  '''+str(data['goodsData']['clickPv'])+'''
'点赞人数'  '''+str(data['interactionData']['likeUv'])+'''
'点赞次数'  '''+str(data['interactionData']['likePv'])+'''
'评论人数'  '''+str(data['interactionData']['commentUv'])+'''
'评论次数'  '''+str(data['interactionData']['commentPv'])+'''
'分享人数'  '''+str(data['interactionData']['shareUv'])+'''
'分享次数'  '''+str(data['interactionData']['sharePv'])
print(str)

headers = {
    'authority': 'live.weixin.qq.com',
    'sec-ch-ua': '^\\^Google',
    'accept': 'application/json, text/plain, */*',
    'dnt': '1',
    'sec-ch-ua-mobile': '?0',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36',
    'content-type': 'application/x-www-form-urlencoded',
    'origin': 'https://live.weixin.qq.com',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'referer': 'https://live.weixin.qq.com/livemp/broadcast/control/53?token=1538867467^&lang=zh_CN',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
    'cookie': 'pgv_info=ssid=s7211604248; pgv_pvid=5856515978; wxuin=10956862149567; ua_id=wNVZExn1CBFYl7BUAAAAAKt7esNrpgne7eLcMOVp_Mg=; openid=o-agI0R31oTrgbYpfFKCyqhPA9wg; data_bizuin=3507005689; slave_user=gh_4632b03820d2; version=1; uuid=de28549e51c79b75e762430dd9396054; slave_sid=bGhjUXZDNXR1eDIzbmxZcEljMFhGOU9uSklobEg5clNwTGtXVWZOVDJOY2hSV25obHZzMWtsSGJTNzh5Uk1OQjVIek9WVmFUTzNnTnFmaEZaeFBaQW83elM1WmdWbkJnX1JncjVXejdEWTVDZnl0ZmtaVmNzSGNUZVpydFBaSnNCTGxheDB1UkYyWGtJOHdt; token=1538867467; rand_info=CAESIDkkavzfDVHqfhTbvt1IwrRx14oOdHPEyguDOIlsCodx',
}

params = (
    ('token', '1538867467'),
    ('lang', 'zh_CN'),
    ('random', '0.24824949352435977'),
)

data = {
  'roomId': '53'
}

# response = requests.post('https://live.weixin.qq.com/livemp/cgi/broadcast/data/getLiveDataControl', headers=headers, params=params, data=data)

str = '{"ret":0,"success":true,"openid":"o-agI0R31oTrgbYpfFKCyqhPA9wg","retList":[{"dataList":[{"dataTimeStamp":1601291943,"cnt":944},{"dataTimeStamp":1601292063,"cnt":1049},{"dataTimeStamp":1601292183,"cnt":1122},{"dataTimeStamp":1601292303,"cnt":1219},{"dataTimeStamp":1601292423,"cnt":1308},{"dataTimeStamp":1601292543,"cnt":1416},{"dataTimeStamp":1601292663,"cnt":1772},{"dataTimeStamp":1601292783,"cnt":2403},{"dataTimeStamp":1601292903,"cnt":3038},{"dataTimeStamp":1601293023,"cnt":3532},{"dataTimeStamp":1601293143,"cnt":3909},{"dataTimeStamp":1601293263,"cnt":4148},{"dataTimeStamp":1601293383,"cnt":4250},{"dataTimeStamp":1601293503,"cnt":4536},{"dataTimeStamp":1601293623,"cnt":5086},{"dataTimeStamp":1601293743,"cnt":5563},{"dataTimeStamp":1601293863,"cnt":5874},{"dataTimeStamp":1601293983,"cnt":6228},{"dataTimeStamp":1601294103,"cnt":6538},{"dataTimeStamp":1601294223,"cnt":6932},{"dataTimeStamp":1601294343,"cnt":7270},{"dataTimeStamp":1601294463,"cnt":7592},{"dataTimeStamp":1601294583,"cnt":7832},{"dataTimeStamp":1601294703,"cnt":8267},{"dataTimeStamp":1601294823,"cnt":8496},{"dataTimeStamp":1601294943,"cnt":8729},{"dataTimeStamp":1601295063,"cnt":9208},{"dataTimeStamp":1601295183,"cnt":9557},{"dataTimeStamp":1601295303,"cnt":10236},{"dataTimeStamp":1601295423,"cnt":10730},{"dataTimeStamp":1601295543,"cnt":11266},{"dataTimeStamp":1601295663,"cnt":12503},{"dataTimeStamp":1601295783,"cnt":13411},{"dataTimeStamp":1601295903,"cnt":14376},{"dataTimeStamp":1601296023,"cnt":15381},{"dataTimeStamp":1601296143,"cnt":16057},{"dataTimeStamp":1601296263,"cnt":16654},{"dataTimeStamp":1601296383,"cnt":17581},{"dataTimeStamp":1601296503,"cnt":18323},{"dataTimeStamp":1601296623,"cnt":19135},{"dataTimeStamp":1601296743,"cnt":19980},{"dataTimeStamp":1601296863,"cnt":20574},{"dataTimeStamp":1601296983,"cnt":21275},{"dataTimeStamp":1601297103,"cnt":21855},{"dataTimeStamp":1601297223,"cnt":22632},{"dataTimeStamp":1601297343,"cnt":23408},{"dataTimeStamp":1601297463,"cnt":24253},{"dataTimeStamp":1601297583,"cnt":24722},{"dataTimeStamp":1601297703,"cnt":24928},{"dataTimeStamp":1601297823,"cnt":25556},{"dataTimeStamp":1601297943,"cnt":26224},{"dataTimeStamp":1601298063,"cnt":26643},{"dataTimeStamp":1601298183,"cnt":27338},{"dataTimeStamp":1601298303,"cnt":28617},{"dataTimeStamp":1601298423,"cnt":29579},{"dataTimeStamp":1601298543,"cnt":29952},{"dataTimeStamp":1601298663,"cnt":30434},{"dataTimeStamp":1601298783,"cnt":31076},{"dataTimeStamp":1601298903,"cnt":31823},{"dataTimeStamp":1601299023,"cnt":32591},{"dataTimeStamp":1601299143,"cnt":33492},{"dataTimeStamp":1601299263,"cnt":34231},{"dataTimeStamp":1601299383,"cnt":35146},{"dataTimeStamp":1601299503,"cnt":36141},{"dataTimeStamp":1601299623,"cnt":37257},{"dataTimeStamp":1601299743,"cnt":38404},{"dataTimeStamp":1601299841,"cnt":39182}]},{"dataList":[{"dataTimeStamp":1601291943,"cnt":48},{"dataTimeStamp":1601292063,"cnt":96},{"dataTimeStamp":1601292183,"cnt":107},{"dataTimeStamp":1601292303,"cnt":138},{"dataTimeStamp":1601292423,"cnt":146},{"dataTimeStamp":1601292543,"cnt":142},{"dataTimeStamp":1601292663,"cnt":119},{"dataTimeStamp":1601292783,"cnt":118},{"dataTimeStamp":1601292903,"cnt":121},{"dataTimeStamp":1601293023,"cnt":120},{"dataTimeStamp":1601293143,"cnt":111},{"dataTimeStamp":1601293263,"cnt":114},{"dataTimeStamp":1601293383,"cnt":108},{"dataTimeStamp":1601293503,"cnt":114},{"dataTimeStamp":1601293623,"cnt":108},{"dataTimeStamp":1601293743,"cnt":107},{"dataTimeStamp":1601293863,"cnt":113},{"dataTimeStamp":1601293983,"cnt":103},{"dataTimeStamp":1601294103,"cnt":116},{"dataTimeStamp":1601294223,"cnt":109},{"dataTimeStamp":1601294343,"cnt":93},{"dataTimeStamp":1601294463,"cnt":102},{"dataTimeStamp":1601294583,"cnt":99},{"dataTimeStamp":1601294703,"cnt":105},{"dataTimeStamp":1601294823,"cnt":99},{"dataTimeStamp":1601294943,"cnt":86},{"dataTimeStamp":1601295063,"cnt":70},{"dataTimeStamp":1601295183,"cnt":69},{"dataTimeStamp":1601295303,"cnt":65},{"dataTimeStamp":1601295423,"cnt":81},{"dataTimeStamp":1601295543,"cnt":77},{"dataTimeStamp":1601295663,"cnt":71},{"dataTimeStamp":1601295783,"cnt":70},{"dataTimeStamp":1601295903,"cnt":69},{"dataTimeStamp":1601296023,"cnt":66},{"dataTimeStamp":1601296143,"cnt":76},{"dataTimeStamp":1601296263,"cnt":68},{"dataTimeStamp":1601296383,"cnt":66},{"dataTimeStamp":1601296503,"cnt":69},{"dataTimeStamp":1601296623,"cnt":65},{"dataTimeStamp":1601296743,"cnt":68},{"dataTimeStamp":1601296863,"cnt":71},{"dataTimeStamp":1601296983,"cnt":76},{"dataTimeStamp":1601297103,"cnt":61},{"dataTimeStamp":1601297223,"cnt":62},{"dataTimeStamp":1601297343,"cnt":59},{"dataTimeStamp":1601297463,"cnt":71},{"dataTimeStamp":1601297583,"cnt":84},{"dataTimeStamp":1601297703,"cnt":100},{"dataTimeStamp":1601297823,"cnt":83},{"dataTimeStamp":1601297943,"cnt":59},{"dataTimeStamp":1601298063,"cnt":58},{"dataTimeStamp":1601298183,"cnt":58},{"dataTimeStamp":1601298303,"cnt":56},{"dataTimeStamp":1601298423,"cnt":56},{"dataTimeStamp":1601298543,"cnt":60},{"dataTimeStamp":1601298663,"cnt":55},{"dataTimeStamp":1601298783,"cnt":47},{"dataTimeStamp":1601298903,"cnt":44},{"dataTimeStamp":1601299023,"cnt":50},{"dataTimeStamp":1601299143,"cnt":48},{"dataTimeStamp":1601299263,"cnt":66},{"dataTimeStamp":1601299383,"cnt":94},{"dataTimeStamp":1601299503,"cnt":61},{"dataTimeStamp":1601299623,"cnt":46},{"dataTimeStamp":1601299743,"cnt":46},{"dataTimeStamp":1601299841,"cnt":48}]},{"dataList":[{"dataTimeStamp":1601291943,"cnt":85},{"dataTimeStamp":1601292063,"cnt":97},{"dataTimeStamp":1601292183,"cnt":109},{"dataTimeStamp":1601292303,"cnt":118},{"dataTimeStamp":1601292423,"cnt":124},{"dataTimeStamp":1601292543,"cnt":136},{"dataTimeStamp":1601292663,"cnt":166},{"dataTimeStamp":1601292783,"cnt":178},{"dataTimeStamp":1601292903,"cnt":208},{"dataTimeStamp":1601293023,"cnt":228},{"dataTimeStamp":1601293143,"cnt":263},{"dataTimeStamp":1601293263,"cnt":307},{"dataTimeStamp":1601293383,"cnt":346},{"dataTimeStamp":1601293503,"cnt":370},{"dataTimeStamp":1601293623,"cnt":389},{"dataTimeStamp":1601293743,"cnt":411},{"dataTimeStamp":1601293863,"cnt":423},{"dataTimeStamp":1601293983,"cnt":441},{"dataTimeStamp":1601294103,"cnt":471},{"dataTimeStamp":1601294223,"cnt":484},{"dataTimeStamp":1601294343,"cnt":498},{"dataTimeStamp":1601294463,"cnt":511},{"dataTimeStamp":1601294583,"cnt":526},{"dataTimeStamp":1601294703,"cnt":556},{"dataTimeStamp":1601294823,"cnt":579},{"dataTimeStamp":1601294943,"cnt":592},{"dataTimeStamp":1601295063,"cnt":609},{"dataTimeStamp":1601295183,"cnt":617},{"dataTimeStamp":1601295303,"cnt":624},{"dataTimeStamp":1601295423,"cnt":630},{"dataTimeStamp":1601295543,"cnt":646},{"dataTimeStamp":1601295663,"cnt":669},{"dataTimeStamp":1601295783,"cnt":693},{"dataTimeStamp":1601295903,"cnt":697},{"dataTimeStamp":1601296023,"cnt":729},{"dataTimeStamp":1601296143,"cnt":745},{"dataTimeStamp":1601296263,"cnt":753},{"dataTimeStamp":1601296383,"cnt":757},{"dataTimeStamp":1601296503,"cnt":771},{"dataTimeStamp":1601296623,"cnt":797},{"dataTimeStamp":1601296743,"cnt":813},{"dataTimeStamp":1601296863,"cnt":834},{"dataTimeStamp":1601296983,"cnt":860},{"dataTimeStamp":1601297103,"cnt":880},{"dataTimeStamp":1601297223,"cnt":904},{"dataTimeStamp":1601297343,"cnt":933},{"dataTimeStamp":1601297463,"cnt":972},{"dataTimeStamp":1601297583,"cnt":1003},{"dataTimeStamp":1601297703,"cnt":1026},{"dataTimeStamp":1601297823,"cnt":1036},{"dataTimeStamp":1601297943,"cnt":1063},{"dataTimeStamp":1601298063,"cnt":1102},{"dataTimeStamp":1601298183,"cnt":1122},{"dataTimeStamp":1601298303,"cnt":1155},{"dataTimeStamp":1601298423,"cnt":1164},{"dataTimeStamp":1601298543,"cnt":1207},{"dataTimeStamp":1601298663,"cnt":1259},{"dataTimeStamp":1601298783,"cnt":1282},{"dataTimeStamp":1601298903,"cnt":1283},{"dataTimeStamp":1601299023,"cnt":1289},{"dataTimeStamp":1601299143,"cnt":1309},{"dataTimeStamp":1601299263,"cnt":1328},{"dataTimeStamp":1601299383,"cnt":1332},{"dataTimeStamp":1601299503,"cnt":1343},{"dataTimeStamp":1601299623,"cnt":1355},{"dataTimeStamp":1601299743,"cnt":1361},{"dataTimeStamp":1601299841,"cnt":1370}]}]}'
data = json.loads(str)
x = []
y = []
xticks=[]
for c,i in enumerate(data['retList'][0]['dataList']):
    # 转换为localtime
    time_local = time.localtime(i['dataTimeStamp'])
    # 转换为新的时间格式
    dt = time.strftime("%H:%M", time_local)
    x.append(dt)
    y.append(i['cnt'])
    if c%3 == 0:
        xticks.append(dt)



print(x)
print(y)
from matplotlib import pyplot
pyplot.figure(figsize=(18, 10.5))
#横坐标
year=x
#纵坐标
perple=y
#生成折线图：函数polt
pyplot.plot(year,perple)
#设置横坐标说明
pyplot.xlabel('time')
#设置纵坐标说明
pyplot.ylabel('count')
#添加标题
pyplot.title('Population year correspondence')
#设置纵坐标刻度
pyplot.xticks(xticks)
# 显示网格
pyplot.grid(True)
#显示图表
pyplot.savefig('a.png')