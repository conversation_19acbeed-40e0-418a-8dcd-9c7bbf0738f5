#-*- coding: UTF-8 -*-
import pymysql
import requests
import json
import datetime
from dateutil.relativedelta import relativedelta


# db=cx_Oracle.connect('fsiepgm/coship@************:1521/ORCL')  #连接user/passwd@host:端口/instance
# cursor = db.cursor() #创建游标对象
# cursor.execute('select sum(t1.play_time)*7.5/8/1024 as 时长 from t_iepg_asset_file t1,t_res_cloumn_map t where t1.resource_id = t.resource_id(+) and  t.column_id in (select t2.column_id from t_column t2 where ((t2.parent_id = 5000051836 AND t2.COLUMN_ID <> 5000051838) OR t2.parent_id = 5000051838))')  #执行命令
# data = cursor.fetchone() #返回值
# print('Database time:%s' % data)  #打印输出
# cursor.close()  #关闭游标对象
# db.close()  #关闭数据库

################################################################################################
################################################################################################

today = datetime.date.today()
first_day_of_the_year = datetime.date.today().replace(month=1).replace(day=1)
first_day_of_the_year = str(first_day_of_the_year)
lmonth = today - relativedelta(months=1)    #上个月
lwmonth = today - relativedelta(months=2)   #上上个月
first_day_of_the_month_before_last = str(datetime.date(lwmonth.year,lwmonth.month,1))               #上上月第一天
last_day_of_the_month_before_last = str(datetime.date(lmonth.year,lmonth.month,1) - relativedelta(days=1)) #上上月最后一天

now_time = datetime.datetime.now()
if(1==now_time.month):
    year=str(now_time.year-1)
    month=str(now_time.month+11)
else:
    year = str(now_time.year)
    month=str(now_time.month)

first_day_of_last_month = str((datetime.date.today().replace(day=1) - datetime.timedelta(1)).replace(day=1))
last_day_of_last_month = str((datetime.date.today().replace(day=1) - datetime.timedelta(days=1)))
print(first_day_of_the_month_before_last,last_day_of_the_month_before_last,first_day_of_last_month,last_day_of_last_month)


# first_day_of_the_month_before_last = "2020-09-01"
# last_day_of_the_month_before_last = "2020-09-30"
data = requests.get('http://**************:8080/vote/getLibraryTotalVisit?jsonData={"startDate":"'+first_day_of_the_month_before_last+' 00:00:00","endDate":"'+last_day_of_the_month_before_last+' 23:59:59"}')
data = json.loads(data.content)
print('上上月总访问量：'+str(data['totalVisit']))
last_zongfangwen=str(data['totalVisit'])
print('上上月电视用户数：'+str(data['tvUserCount']))
last_dianshiyonghu=str(data['tvUserCount'])
print('上上月佛图用户数：'+str(data['libraryUserCount'])+'\n')
last_fotuyonghu=str(data['libraryUserCount'])

# first_day_of_last_month = "2020-10-01"
# last_day_of_last_month = "2020-10-28"     要加年月日
data = requests.get('http://**************:8080/vote/getLibraryTotalVisit?jsonData={"startDate":"'+first_day_of_last_month+'","endDate":"'+last_day_of_last_month+'"}')

data = json.loads(data.content)
print('总访问量：'+str(data['totalVisit']))
zongfangwen=str(data['totalVisit'])
print('电视用户数：'+str(data['tvUserCount']))
dianshiyonghu=str(data['tvUserCount'])
print('佛图用户数：'+str(data['libraryUserCount'])+'\n')
fotuyonghu=str(data['libraryUserCount'])


data = requests.get('http://**************:8080/vote/getLibraryTotalVisit?jsonData={"startDate":"'+first_day_of_the_year+'","endDate":"'+last_day_of_last_month+'"}')
data = json.loads(data.content)
print('年总访问量：'+str(data['totalVisit']))
nianzongfangwen=str(data['totalVisit'])


################################################################################################
################################################################################################
db = pymysql.connect(
    host="127.0.0.1",
    port=3326,
    user="root",
    password="FScatv@2016",
    database="newfaith")
cursor = db.cursor()
cursor.execute("SELECT COUNT(*) FROM t_upshelf_column a LEFT JOIN t_column b ON a.`columnId`=b.`id` WHERE b.`serviceId`=5 AND SUBSTR(a.`upshelfTime`,1,4)='"+year+"'")
gelei = cursor.fetchone()
print('各类信息：%s' % gelei)
gelei='%s' % gelei
cursor.execute("select count(*) from t_library_asset where examineStatus=1 and substr(operationTime,1,4)='"+year+"'")
xinzeng = cursor.fetchone()
print('新增视频：%s' % xinzeng)
xinzeng='%s' % xinzeng
cursor.execute("SELECT COUNT(*) FROM t_upshelf_column a LEFT JOIN t_column b ON a.`columnId`=b.`id` WHERE b.`serviceId`=5 AND SUBSTR(a.`upshelfTime`,1,4)='"+year+"' AND b.`id`='955'")
xinshu = cursor.fetchone()
print('新书推荐：%s' % xinshu)
xinshu='%s' % xinshu
db.close()



import docxtpl
from docx.shared import Mm, Inches, Pt
import jinja2
tpl = docxtpl.DocxTemplate('C:/Users/<USER>/Desktop/monthly_report/template/test.docx')
context = {}

for i in range(0,54):
    context.setdefault('image'+str(i) , docxtpl.InlineImage(tpl,'C:/Users/<USER>/Desktop/monthly_report/template/Graphs __ Tree Mode/pic_'+str(i)+'.png',width=Mm(150)))

context.setdefault('time',first_day_of_last_month+'至'+last_day_of_last_month)
context.setdefault('year',year)
context.setdefault('month',str(now_time.month-1))
context.setdefault('lastzongfangwen',last_zongfangwen)
context.setdefault('lastdianshiyonghu',last_dianshiyonghu)
context.setdefault('lastfotuyonghu',last_fotuyonghu)
context.setdefault('zongfangwen',zongfangwen)
context.setdefault('nianzongfangwen',nianzongfangwen)
context.setdefault('dianshiyonghu',dianshiyonghu)
context.setdefault('fotuyonghu',fotuyonghu)
context.setdefault('gelei',gelei)
context.setdefault('xinzeng',xinzeng)
context.setdefault('xinshu',xinshu)

jinja_env = jinja2.Environment(autoescape=True)
tpl.render(context, jinja_env)
tpl.save('C:/Users/<USER>/Desktop/monthly_report/电视图书馆'+year+'年'+str(now_time.month-1)+'月月报'+'.docx')
print('报告已生成')
