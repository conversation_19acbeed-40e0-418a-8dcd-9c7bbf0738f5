#-*- coding: UTF-8 -*-

# import cx_Oracle
#
# db=cx_Oracle.connect('fsiepgm/coship@************:1521/ORCL')  #连接user/passwd@host:端口/instance
# cursor = db.cursor() #创建游标对象
# cursor.execute('select sum(t1.play_time)*7.5/8/1024 as 时长 from t_iepg_asset_file t1,t_res_cloumn_map t where t1.resource_id = t.resource_id(+) and  t.column_id in (select t2.column_id from t_column t2 where ((t2.parent_id = 5000051836 AND t2.COLUMN_ID <> 5000051838) OR t2.parent_id = 5000051838))')  #执行命令
# data = cursor.fetchone() #返回值
# print('Database time:%s' % data)  #打印输出
# cursor.close()  #关闭游标对象
# db.close()  #关闭数据库

################################################################################################
################################################################################################
#
#
# import requests
# import json
# import time
# import datetime
# cookies = {
#     'login_sid_t': '08ce1d8d28a2962e2ad41769e53a8ec3',
#     'cross_origin_proto': 'SSL',
#     '_s_tentry': 'passport.weibo.com',
#     'Apache': '8315705383297.434.1613801946229',
#     'SINAGLOBAL': '8315705383297.434.1613801946229',
#     'ULV': '1613801946252:1:1:1:8315705383297.434.1613801946229:',
#     'SUBP': '0033WrSXqPxfM725Ws9jqgMF55529P9D9WFMg.PLbQjCdrdY3PCJ2.Dq5JpX5o275NHD95QpS0zNSonR1hepWs4DqcjzPfDAgrDAw.2t',
#     'SSOLoginState': '1613802017',
#     'SUB': '_2A25NNNpxDeRhGedJ6VUX-CbEyD2IHXVuQEy5rDV8PUNbmtANLRnMkW9NVlRUNHxWE6avX0Slcvy6CrgvWYQRGkRO',
#     'ALF': '1645338016',
#     'wvr': '6',
#     'webim_unReadCount': '^%^7B^%^22time^%^22^%^3A1613802843269^%^2C^%^22dm_pub_total^%^22^%^3A9^%^2C^%^22chat_group_client^%^22^%^3A0^%^2C^%^22chat_group_notice^%^22^%^3A0^%^2C^%^22allcountNum^%^22^%^3A39^%^2C^%^22msgbox^%^22^%^3A0^%^7D',
#     'JSESSIONID': '********************************',
# }
#
# headers = {
#     'Connection': 'keep-alive',
#     'sec-ch-ua': '^\\^Chromium^\\^;v=^\\^88^\\^, ^\\^Google',
#     'Accept': 'application/json, text/plain, */*',
#     'DNT': '1',
#     'sec-ch-ua-mobile': '?0',
#     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36',
#     'Sec-Fetch-Site': 'same-origin',
#     'Sec-Fetch-Mode': 'cors',
#     'Sec-Fetch-Dest': 'empty',
#     'Referer': 'https://api.weibo.com/chat/',
#     'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
# }
#
# params = (
#     ('convert_emoji', '1'),
#     ('query_sender', '1'),
#     ('count', '1'),
#     ('id', '4293620976457429'),
#     ('max_mid', '0'),
#     ('source', '209678993'),
#     ('t', str(int(round(time.time() * 1000)))),
# )
#
# response = requests.get('https://api.weibo.com/webim/groupchat/query_messages.json', headers=headers, params=params, cookies=cookies)
# res = json.loads(response.content.decode('UTF-8'))
# print(json.dumps(res, indent=2).encode('utf-8').decode('unicode_escape'))
# print(int(round(time.time() * 1000)))
# response = requests.get('https://api.weibo.com/webim/groupchat/query_messages.json?convert_emoji=1&query_sender=1&count=20&id=4293620976457429&max_mid=4606674067787487&source=209678993&t=1613803151034', headers=headers, cookies=cookies)
import time

import openpyxl
import pymysql
import requests
import json


wb = openpyxl.load_workbook('尾数重复几次.xlsx')
active_sheet = wb['Sheet1']
temp = []
for i in range(1,active_sheet.max_row+1):
    s = str(active_sheet.cell(i, 1).value)
    s = s.split('.')[0]
    temp.append(s)


rest = []
for i in temp:
    i = i[::-1]
    last = i[0]
    con = len(i)
    num = 0
    for j in range(0,con):
        if i[j] == last:
            num = num +1
            if j+1 == con:
                rest.append(num)
        else:
            rest.append(num)
            break



# outwb = openpyxl.Workbook()  # 打开一个将写的文件
# outws = outwb.create_sheet(index=0)  # 在将写的文件创建sheet
for row in range(0, len(rest)):
    active_sheet.cell(row + 1, 2).value = rest[row]  # 写文件

saveExcel = "C:\\Users\\<USER>\\Desktop\\test.xlsx"
wb.save(saveExcel)  # 一定要记得保存



